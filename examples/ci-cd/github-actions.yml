name: Laravel Checker Quality Gate

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, dom, filter, gd, iconv, json, mbstring, pdo
        coverage: xdebug
        
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-
          
    - name: Install PHP dependencies
      run: composer install --prefer-dist --no-progress
      
    - name: Install Node dependencies
      run: npm ci
      
    - name: Install Laravel Checker
      run: composer global require zteam/laravel-checker
      
    - name: Prepare <PERSON> application
      run: |
        php -r "file_exists('.env') || copy('.env.example', '.env');"
        php artisan key:generate
        php artisan config:cache
        
    - name: Run tests with coverage
      run: |
        php artisan test --coverage-clover coverage/clover.xml
        npm run test -- --coverage
        
    - name: Run Laravel Checker
      run: |
        ~/.composer/vendor/bin/laravel-checker \
          --format=json \
          --format=html \
          --output=./quality-reports \
          --config=.laravel-checker.json
          
    - name: Check quality gates
      run: |
        # Extract scores from JSON report
        OVERALL_SCORE=$(cat quality-reports/summary.json | jq '.overall_score')
        SECURITY_ISSUES=$(cat quality-reports/summary.json | jq '.security_issues')
        VULNERABLE_DEPS=$(cat quality-reports/summary.json | jq '.vulnerable_deps')
        
        echo "Overall Score: $OVERALL_SCORE"
        echo "Security Issues: $SECURITY_ISSUES"
        echo "Vulnerable Dependencies: $VULNERABLE_DEPS"
        
        # Fail if critical issues found
        if [ "$SECURITY_ISSUES" -gt 0 ]; then
          echo "❌ Security issues found - blocking deployment"
          exit 1
        fi
        
        if [ "$VULNERABLE_DEPS" -gt 0 ]; then
          echo "❌ Vulnerable dependencies found - blocking deployment"
          exit 1
        fi
        
        if (( $(echo "$OVERALL_SCORE < 60" | bc -l) )); then
          echo "❌ Overall quality score too low - blocking deployment"
          exit 1
        fi
        
        echo "✅ All quality gates passed"
        
    - name: Upload quality reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: quality-reports
        path: quality-reports/
        
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const summary = JSON.parse(fs.readFileSync('quality-reports/summary.json', 'utf8'));
          
          const comment = `## 📊 Code Quality Report
          
          | Metric | Score | Status |
          |--------|-------|--------|
          | Overall | ${summary.overall_score.toFixed(1)}/100 | ${summary.overall_score >= 80 ? '✅' : summary.overall_score >= 60 ? '⚠️' : '❌'} |
          | Quality | ${summary.quality_score.toFixed(1)}/100 | ${summary.quality_score >= 80 ? '✅' : summary.quality_score >= 60 ? '⚠️' : '❌'} |
          | Security | ${summary.security_score.toFixed(1)}/100 | ${summary.security_score >= 80 ? '✅' : summary.security_score >= 60 ? '⚠️' : '❌'} |
          | Coverage | ${summary.test_coverage.toFixed(1)}% | ${summary.test_coverage >= 80 ? '✅' : summary.test_coverage >= 60 ? '⚠️' : '❌'} |
          
          **Issues Found:**
          - 🐛 Code Issues: ${summary.code_issues}
          - 🔒 Security Issues: ${summary.security_issues}
          - 📦 Outdated Dependencies: ${summary.outdated_deps}
          
          **Release Readiness:** ${summary.release_readiness === 'ready' ? '✅ Ready' : summary.release_readiness === 'ready_with_warnings' ? '⚠️ Ready with warnings' : '❌ Not ready'}
          
          [View detailed report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  deploy:
    needs: quality-check
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production..."
        # Add your deployment steps here
