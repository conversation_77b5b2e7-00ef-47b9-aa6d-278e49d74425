pipeline {
    agent any
    
    environment {
        PHP_VERSION = '8.1'
        NODE_VERSION = '18'
        COMPOSER_CACHE_DIR = '/tmp/composer-cache'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
                
                // Clean workspace
                sh 'rm -rf quality-reports'
                sh 'mkdir -p quality-reports'
            }
        }
        
        stage('Setup Environment') {
            parallel {
                stage('Setup PHP') {
                    steps {
                        sh '''
                            # Install PHP dependencies
                            composer install --prefer-dist --no-progress --no-dev
                            
                            # Install Laravel Checker globally
                            composer global require zteam/laravel-checker
                        '''
                    }
                }
                
                stage('Setup Node.js') {
                    steps {
                        sh '''
                            # Install Node.js dependencies
                            npm ci --production=false
                        '''
                    }
                }
            }
        }
        
        stage('Prepare Application') {
            steps {
                sh '''
                    # Setup Laravel environment
                    cp .env.example .env
                    php artisan key:generate
                    php artisan config:cache
                    
                    # Create database for testing
                    touch database/database.sqlite
                '''
            }
        }
        
        stage('Run Tests') {
            parallel {
                stage('PHP Tests') {
                    steps {
                        sh '''
                            # Run PHPUnit tests with coverage
                            php artisan test --coverage-clover=coverage/clover.xml
                        '''
                    }
                    post {
                        always {
                            // Publish test results
                            publishTestResults testResultsPattern: 'tests/results.xml'
                            
                            // Publish coverage
                            publishCoverage adapters: [
                                cloverAdapter('coverage/clover.xml')
                            ], sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
                        }
                    }
                }
                
                stage('JavaScript Tests') {
                    steps {
                        sh '''
                            # Run Jest tests with coverage
                            npm run test -- --coverage --ci --watchAll=false
                        '''
                    }
                }
            }
        }
        
        stage('Quality Analysis') {
            steps {
                script {
                    // Run Laravel Checker
                    sh '''
                        ~/.composer/vendor/bin/laravel-checker \\
                            --format=json \\
                            --format=html \\
                            --format=console \\
                            --output=./quality-reports \\
                            --config=.laravel-checker.json
                    '''
                    
                    // Read quality results
                    def qualityResults = readJSON file: 'quality-reports/summary.json'
                    
                    // Set build properties
                    currentBuild.description = """
                        Quality: ${qualityResults.quality_score}/100 | 
                        Security: ${qualityResults.security_score}/100 | 
                        Coverage: ${qualityResults.test_coverage}%
                    """
                    
                    // Store results as build artifacts
                    env.OVERALL_SCORE = qualityResults.overall_score
                    env.SECURITY_ISSUES = qualityResults.security_issues
                    env.VULNERABLE_DEPS = qualityResults.vulnerable_deps
                    env.RELEASE_READINESS = qualityResults.release_readiness
                }
            }
            post {
                always {
                    // Archive quality reports
                    archiveArtifacts artifacts: 'quality-reports/**/*', fingerprint: true
                    
                    // Publish HTML report
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'quality-reports',
                        reportFiles: 'laravel-checker-report.html',
                        reportName: 'Laravel Checker Report',
                        reportTitles: 'Quality Analysis Report'
                    ])
                }
            }
        }
        
        stage('Quality Gates') {
            steps {
                script {
                    def overallScore = env.OVERALL_SCORE as Double
                    def securityIssues = env.SECURITY_ISSUES as Integer
                    def vulnerableDeps = env.VULNERABLE_DEPS as Integer
                    
                    echo "Quality Gate Check:"
                    echo "- Overall Score: ${overallScore}/100"
                    echo "- Security Issues: ${securityIssues}"
                    echo "- Vulnerable Dependencies: ${vulnerableDeps}"
                    echo "- Release Readiness: ${env.RELEASE_READINESS}"
                    
                    // Critical blockers
                    if (securityIssues > 0) {
                        error("❌ Security issues found - blocking deployment")
                    }
                    
                    if (vulnerableDeps > 0) {
                        error("❌ Vulnerable dependencies found - blocking deployment")
                    }
                    
                    // Quality gates
                    if (overallScore < 60) {
                        error("❌ Overall quality score too low (${overallScore}/100) - blocking deployment")
                    }
                    
                    if (overallScore < 80) {
                        unstable("⚠️ Quality score below recommended threshold (${overallScore}/100)")
                    }
                    
                    echo "✅ All quality gates passed"
                }
            }
        }
        
        stage('Deploy') {
            when {
                anyOf {
                    branch 'main'
                    branch 'master'
                }
            }
            steps {
                script {
                    if (env.RELEASE_READINESS == 'ready') {
                        echo "🚀 Deploying to production..."
                        
                        // Add your deployment steps here
                        sh '''
                            # Example deployment commands
                            # rsync -avz --exclude-from='.deployignore' ./ user@server:/path/to/app/
                            # ssh user@server 'cd /path/to/app && php artisan migrate --force'
                            # ssh user@server 'cd /path/to/app && php artisan config:cache'
                            # ssh user@server 'cd /path/to/app && php artisan route:cache'
                            
                            echo "Deployment completed successfully"
                        '''
                    } else {
                        echo "⚠️ Deployment skipped - release readiness: ${env.RELEASE_READINESS}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            // Clean up
            sh 'rm -rf coverage/'
            
            // Send notifications
            script {
                def status = currentBuild.result ?: 'SUCCESS'
                def color = status == 'SUCCESS' ? 'good' : status == 'UNSTABLE' ? 'warning' : 'danger'
                def message = """
                    *Laravel Checker Report - ${env.JOB_NAME} #${env.BUILD_NUMBER}*
                    
                    *Status:* ${status}
                    *Branch:* ${env.BRANCH_NAME}
                    *Overall Score:* ${env.OVERALL_SCORE}/100
                    *Release Readiness:* ${env.RELEASE_READINESS}
                    
                    *Security Issues:* ${env.SECURITY_ISSUES}
                    *Vulnerable Dependencies:* ${env.VULNERABLE_DEPS}
                    
                    <${env.BUILD_URL}|View Build> | <${env.BUILD_URL}Laravel_20Checker_20Report/|View Report>
                """
                
                // Send to Slack (if configured)
                // slackSend(color: color, message: message, channel: '#development')
                
                // Send email notification
                emailext(
                    subject: "Laravel Checker Report - ${env.JOB_NAME} #${env.BUILD_NUMBER} - ${status}",
                    body: message,
                    to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
                )
            }
        }
        
        success {
            echo "✅ Pipeline completed successfully"
        }
        
        failure {
            echo "❌ Pipeline failed"
        }
        
        unstable {
            echo "⚠️ Pipeline completed with warnings"
        }
    }
}
