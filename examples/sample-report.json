{"meta": {"tool": "<PERSON><PERSON>", "version": "1.0.0", "scan_time": "2024-01-15 14:30:25", "project_path": "/path/to/laravel-project", "options": {"exclude_dirs": ["vendor", "node_modules"], "skip_security": false, "skip_quality": false, "skip_dependencies": false, "skip_coverage": false}}, "summary": {"overall_score": 78.5, "quality_score": 75.2, "security_score": 85.0, "coverage_score": 72.3, "dependency_score": 82.1, "total_files": 145, "total_lines": 12847, "code_issues": 23, "security_issues": 2, "outdated_deps": 5, "vulnerable_deps": 1, "test_coverage": 72.3, "release_readiness": "ready_with_warnings", "recommendations": [{"category": "security", "priority": "high", "message": "Found 2 security vulnerabilities. Address these before release.", "action": "Review and fix all security vulnerabilities"}, {"category": "testing", "priority": "medium", "message": "Test coverage is 72.3%, below the minimum requirement of 80%.", "action": "Write additional tests to improve coverage"}]}, "quality": {"score": 75.2, "total_files": 145, "total_lines": 12847, "issues": {"total": 23, "by_severity": {"error": 2, "warning": 15, "info": 6}, "by_type": {"high_complexity": 5, "long_method": 8, "coding_standard": 10}, "details": [{"type": "high_complexity", "severity": "warning", "file": "app/Services/PaymentService.php", "line": 45, "message": "Method has high cyclomatic complexity: 12 (max: 10)", "complexity": 12}, {"type": "long_method", "severity": "warning", "file": "app/Http/Controllers/UserController.php", "line": 120, "message": "Method is too long: 65 lines (max: 50)", "length": 65}]}, "tools_used": ["phpcs", "phpstan", "eslint"]}, "security": {"score": 85.0, "total_vulnerabilities": 2, "vulnerabilities": {"by_severity": {"critical": 0, "high": 1, "medium": 1, "low": 0}, "by_type": {"sql_injection": 1, "xss": 1}, "details": [{"type": "sql_injection", "severity": "high", "file": "app/Models/User.php", "line": 78, "message": "Potential SQL injection vulnerability detected", "code_snippet": "DB::raw('SELECT * FROM users WHERE id = ' . $id)", "language": "php"}, {"type": "xss", "severity": "medium", "file": "resources/views/profile.blade.php", "line": 25, "message": "Potential Cross-Site Scripting (XSS) vulnerability detected", "code_snippet": "{!! $user->bio !!}", "language": "php"}]}, "files_scanned": 145}, "dependencies": {"total_dependencies": 45, "outdated": {"count": 5, "packages": [{"name": "laravel/framework", "current_version": "9.0.0", "latest_version": "10.2.5", "type": "composer", "age_days": 180}, {"name": "vue", "current_version": "2.6.14", "latest_version": "3.3.4", "type": "npm", "age_days": 365}]}, "vulnerable": {"count": 1, "packages": [{"package": "axios", "version": "0.21.0", "vulnerability": "Server-Side Request Forgery", "severity": "medium", "cve": "CVE-2021-3749", "link": "https://nvd.nist.gov/vuln/detail/CVE-2021-3749", "type": "npm"}]}, "composer": {"dependencies": {"laravel/framework": "^9.0", "guzzlehttp/guzzle": "^7.0"}, "outdated": [{"name": "laravel/framework", "current_version": "9.0.0", "latest_version": "10.2.5", "type": "composer", "age_days": 180}], "vulnerabilities": []}, "npm": {"dependencies": {"vue": "^2.6.14", "axios": "^0.21.0"}, "outdated": [{"name": "vue", "current_version": "2.6.14", "latest_version": "3.3.4", "type": "npm", "age_days": 365}], "vulnerabilities": [{"package": "axios", "version": "0.21.0", "vulnerability": "Server-Side Request Forgery", "severity": "medium", "cve": "CVE-2021-3749", "type": "npm"}]}, "tools_used": ["composer", "npm"]}, "coverage": {"overall_coverage": 72.3, "line_coverage": 72.3, "branch_coverage": 68.5, "function_coverage": 78.2, "files_covered": 98, "total_files": 145, "uncovered_files": [{"file": "app/Services/ReportService.php", "coverage": 0, "lines_total": 150, "lines_covered": 0}], "php_coverage": {"line_coverage": 75.5, "method_coverage": 80.2, "class_coverage": 85.0, "covered_files": [{"file": "app/Models/User.php", "coverage": 85.5, "lines_total": 120, "lines_covered": 103}], "uncovered_files": []}, "js_coverage": {"line_coverage": 65.2, "branch_coverage": 60.1, "function_coverage": 70.5, "statement_coverage": 65.8, "covered_files": [], "uncovered_files": []}, "tools_used": ["phpunit-clover", "jest-coverage"]}}