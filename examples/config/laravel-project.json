{"exclude_dirs": ["vendor", "node_modules", ".git", "storage/logs", "storage/cache", "storage/framework/cache", "storage/framework/sessions", "storage/framework/views", "bootstrap/cache", "public/storage", "tests/coverage", ".phpunit.cache", "coverage", "build"], "file_extensions": {"php": ["php"], "javascript": ["js", "jsx", "ts", "tsx"], "vue": ["vue"], "css": ["css", "scss", "sass", "less"], "html": ["html", "blade.php"]}, "quality_rules": {"max_complexity": 8, "max_method_length": 40, "max_class_length": 400, "min_test_coverage": 80, "max_nesting_level": 3, "max_parameters": 4}, "security_rules": {"check_sql_injection": true, "check_xss": true, "check_csrf": true, "check_file_upload": true, "check_authentication": true, "check_authorization": true, "check_input_validation": true, "check_output_encoding": true}, "dependency_rules": {"check_outdated": true, "check_vulnerabilities": true, "max_outdated_days": 180, "severity_threshold": "medium"}, "coverage_rules": {"min_line_coverage": 80, "min_branch_coverage": 70, "min_function_coverage": 85}, "tools": {"phpcs": {"enabled": true, "standard": "PSR12", "ignore_patterns": ["*/migrations/*", "*/seeds/*", "*/config/*"]}, "phpstan": {"enabled": true, "level": 8, "paths": ["app", "tests"], "ignore_errors": ["#Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder#"]}, "psalm": {"enabled": false, "config": "psalm.xml"}, "eslint": {"enabled": true, "config": ".eslintrc.js", "ignore_patterns": ["node_modules", "public/js/vendor"]}, "npm_audit": {"enabled": true, "audit_level": "moderate"}, "composer_audit": {"enabled": true}}, "laravel_specific": {"check_env_example": true, "check_config_cache": true, "check_route_cache": true, "check_migrations": true, "check_middleware": true, "check_policies": true, "required_middleware": ["auth", "csrf", "throttle"], "forbidden_functions": ["dd", "dump", "var_dump", "print_r"]}, "performance_rules": {"check_n_plus_one": true, "check_large_collections": true, "max_collection_size": 1000, "check_memory_usage": true}, "documentation_rules": {"require_class_docblocks": true, "require_method_docblocks": true, "require_property_docblocks": false}}