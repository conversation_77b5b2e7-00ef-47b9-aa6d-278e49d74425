{"name": "zteam/laravel-checker", "description": "Comprehensive web project scanner for quality, security, and release readiness assessment", "type": "project", "license": "MIT", "authors": [{"name": "ZTeam", "email": "<EMAIL>"}], "require": {"php": "^8.0", "symfony/console": "^6.0", "symfony/process": "^6.0", "symfony/finder": "^6.0", "symfony/yaml": "^6.0", "twig/twig": "^3.0", "guzzlehttp/guzzle": "^7.0", "nikic/php-parser": "^4.0"}, "require-dev": {"phpunit/phpunit": "^10.0", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^1.0", "psalm/psalm": "^5.0"}, "autoload": {"psr-4": {"ZTeam\\LaravelChecker\\": "src/"}}, "autoload-dev": {"psr-4": {"ZTeam\\LaravelChecker\\Tests\\": "tests/"}}, "bin": ["bin/laravel-checker"], "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyze": "phpstan analyse src/ --level=8", "psalm": "psalm"}, "config": {"sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true}