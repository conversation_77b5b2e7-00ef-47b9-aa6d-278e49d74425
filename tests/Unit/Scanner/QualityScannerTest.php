<?php

declare(strict_types=1);

namespace ZTeam\LaravelChecker\Tests\Unit\Scanner;

use PHPUnit\Framework\TestCase;
use ZTeam\LaravelChecker\Config\Configuration;
use ZTeam\LaravelChecker\Scanner\QualityScanner;

/**
 * Test case for QualityScanner
 */
class QualityScannerTest extends TestCase
{
    private QualityScanner $scanner;
    private Configuration $config;
    private string $testProjectPath;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->config = new Configuration();
        $this->scanner = new QualityScanner($this->config);
        $this->testProjectPath = __DIR__ . '/../../fixtures/test-project';
        
        // Create test project structure
        $this->createTestProject();
    }

    protected function tearDown(): void
    {
        // Clean up test project
        $this->cleanupTestProject();
        parent::tearDown();
    }

    public function testScanReturnsValidStructure(): void
    {
        $results = $this->scanner->scan($this->testProjectPath);

        $this->assertIsArray($results);
        $this->assertArrayHasKey('total_files', $results);
        $this->assertArrayHasKey('total_lines', $results);
        $this->assertArrayHasKey('issues', $results);
        $this->assertArrayHasKey('metrics', $results);
        $this->assertArrayHasKey('tools_used', $results);
    }

    public function testScanPhpFiles(): void
    {
        $results = $this->scanner->scan($this->testProjectPath);

        $this->assertArrayHasKey('php', $results);
        $this->assertGreaterThan(0, $results['php']['files_scanned']);
        $this->assertGreaterThan(0, $results['php']['lines_of_code']);
    }

    public function testDetectHighComplexity(): void
    {
        // Create a PHP file with high complexity
        $complexCode = '<?php
class ComplexClass {
    public function complexMethod($param) {
        if ($param > 0) {
            if ($param < 10) {
                for ($i = 0; $i < $param; $i++) {
                    if ($i % 2 === 0) {
                        if ($i > 5) {
                            while ($i < 8) {
                                switch ($i) {
                                    case 6:
                                        return "six";
                                    case 7:
                                        return "seven";
                                    default:
                                        $i++;
                                }
                            }
                        }
                    }
                }
            }
        }
        return "default";
    }
}';

        file_put_contents($this->testProjectPath . '/ComplexClass.php', $complexCode);

        $results = $this->scanner->scan($this->testProjectPath);

        // Should detect high complexity issue
        $hasComplexityIssue = false;
        foreach ($results['php']['issues'] as $issue) {
            if ($issue['type'] === 'high_complexity') {
                $hasComplexityIssue = true;
                break;
            }
        }

        $this->assertTrue($hasComplexityIssue, 'Should detect high complexity issue');
    }

    public function testDetectLongMethod(): void
    {
        // Create a PHP file with a long method
        $longMethodCode = '<?php
class LongMethodClass {
    public function longMethod() {
        ' . str_repeat('$var = "line";\n        ', 60) . '
        return $var;
    }
}';

        file_put_contents($this->testProjectPath . '/LongMethodClass.php', $longMethodCode);

        $results = $this->scanner->scan($this->testProjectPath);

        // Should detect long method issue
        $hasLongMethodIssue = false;
        foreach ($results['php']['issues'] as $issue) {
            if ($issue['type'] === 'long_method') {
                $hasLongMethodIssue = true;
                break;
            }
        }

        $this->assertTrue($hasLongMethodIssue, 'Should detect long method issue');
    }

    public function testSyntaxErrorDetection(): void
    {
        // Create a PHP file with syntax error
        $syntaxErrorCode = '<?php
class SyntaxErrorClass {
    public function method() {
        $var = "unclosed string;
    }
}';

        file_put_contents($this->testProjectPath . '/SyntaxErrorClass.php', $syntaxErrorCode);

        $results = $this->scanner->scan($this->testProjectPath);

        // Should detect syntax error
        $hasSyntaxError = false;
        foreach ($results['php']['issues'] as $issue) {
            if ($issue['type'] === 'syntax_error') {
                $hasSyntaxError = true;
                break;
            }
        }

        $this->assertTrue($hasSyntaxError, 'Should detect syntax error');
    }

    public function testQualityScoreCalculation(): void
    {
        $results = $this->scanner->scan($this->testProjectPath);

        $this->assertArrayHasKey('quality_score', $results['metrics']);
        $this->assertIsFloat($results['metrics']['quality_score']);
        $this->assertGreaterThanOrEqual(0, $results['metrics']['quality_score']);
        $this->assertLessThanOrEqual(100, $results['metrics']['quality_score']);
    }

    public function testExcludeDirectories(): void
    {
        // Create excluded directory
        mkdir($this->testProjectPath . '/vendor', 0755, true);
        file_put_contents($this->testProjectPath . '/vendor/test.php', '<?php echo "vendor file";');

        $options = ['exclude_dirs' => ['vendor']];
        $results = $this->scanner->scan($this->testProjectPath, $options);

        // Should not scan vendor directory
        $vendorFileScanned = false;
        foreach ($results['php']['issues'] as $issue) {
            if (str_contains($issue['file'], 'vendor/')) {
                $vendorFileScanned = true;
                break;
            }
        }

        $this->assertFalse($vendorFileScanned, 'Should not scan excluded directories');
    }

    private function createTestProject(): void
    {
        if (!is_dir($this->testProjectPath)) {
            mkdir($this->testProjectPath, 0755, true);
        }

        // Create a simple PHP file
        $simpleCode = '<?php
namespace App;

class SimpleClass {
    private $property;

    public function simpleMethod() {
        return "simple";
    }
}';

        file_put_contents($this->testProjectPath . '/SimpleClass.php', $simpleCode);

        // Create a JavaScript file
        $jsCode = 'function simpleFunction() {
    return "simple";
}

const arrow = () => {
    console.log("arrow function");
};';

        file_put_contents($this->testProjectPath . '/simple.js', $jsCode);
    }

    private function cleanupTestProject(): void
    {
        if (is_dir($this->testProjectPath)) {
            $this->removeDirectory($this->testProjectPath);
        }
    }

    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}
