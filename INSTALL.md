# Hướng dẫn cài đặt Laravel Checker

## Y<PERSON>u c<PERSON>u hệ thống

### Bắt buộc
- **PHP**: 8.0 hoặc cao hơn
- **Composer**: 2.0 hoặc cao hơn
- **Extensions**: mbstring, xml, json, tokenizer

### T<PERSON><PERSON> chọn (để có đầy đủ tính năng)
- **Node.js**: 16.0 hoặc cao hơn
- **NPM**: 8.0 hoặc cao hơn

## Cài đặt

### 1. <PERSON><PERSON><PERSON> đặt qua Composer (Khuyến nghị)

```bash
# Cài đặt global
composer global require zteam/laravel-checker

# Hoặc cài đặt cho dự án cụ thể
composer require --dev zteam/laravel-checker
```

### 2. Cài đặt từ source

```bash
# Clone repository
git clone https://github.com/zteam/laravel-checker.git
cd laravel-checker

# Cài đặt dependencies
composer install

# C<PERSON><PERSON> quyền thực thi
chmod +x bin/laravel-checker

# Tạo symlink (tù<PERSON> chọn)
sudo ln -s $(pwd)/bin/laravel-checker /usr/local/bin/laravel-checker
```

### 3. Cài đặt Docker

```bash
# Pull image
docker pull zteam/laravel-checker:latest

# Chạy container
docker run --rm -v $(pwd):/app zteam/laravel-checker
```

## Cài đặt công cụ bổ sung

### PHP Tools

```bash
# PHP_CodeSniffer
composer global require squizlabs/php_codesniffer

# PHPStan
composer global require phpstan/phpstan

# Psalm
composer global require vimeo/psalm
```

### JavaScript Tools

```bash
# ESLint
npm install -g eslint

# Jest (cho testing)
npm install -g jest
```

## Kiểm tra cài đặt

```bash
# Kiểm tra Laravel Checker
laravel-checker --version

# Kiểm tra các công cụ bổ sung
phpcs --version
phpstan --version
eslint --version
```

## Cấu hình ban đầu

### 1. Tạo file cấu hình

```bash
# Tạo file cấu hình mặc định
laravel-checker --init

# Hoặc copy từ template
cp vendor/zteam/laravel-checker/config/default.json .laravel-checker.json
```

### 2. Tùy chỉnh cấu hình

Chỉnh sửa file `.laravel-checker.json`:

```json
{
    "exclude_dirs": [
        "vendor",
        "node_modules",
        "storage/logs"
    ],
    "quality_rules": {
        "max_complexity": 10,
        "min_test_coverage": 80
    },
    "tools": {
        "phpcs": {
            "enabled": true,
            "standard": "PSR12"
        }
    }
}
```

## Chạy lần đầu

```bash
# Quét dự án hiện tại
laravel-checker

# Quét với báo cáo HTML
laravel-checker --format=html --output=./reports
```

## Xử lý sự cố

### Lỗi "Command not found"

```bash
# Kiểm tra PATH
echo $PATH

# Thêm Composer global bin vào PATH
export PATH="$PATH:$HOME/.composer/vendor/bin"

# Hoặc thêm vào ~/.bashrc hoặc ~/.zshrc
echo 'export PATH="$PATH:$HOME/.composer/vendor/bin"' >> ~/.bashrc
```

### Lỗi "Permission denied"

```bash
# Cấp quyền thực thi
chmod +x bin/laravel-checker

# Hoặc chạy với php
php bin/laravel-checker
```

### Lỗi "Class not found"

```bash
# Cập nhật autoloader
composer dump-autoload

# Kiểm tra cài đặt
composer validate
```

### Lỗi memory limit

```bash
# Tăng memory limit
php -d memory_limit=512M bin/laravel-checker

# Hoặc chỉnh sửa php.ini
memory_limit = 512M
```

## Cập nhật

```bash
# Cập nhật qua Composer
composer global update zteam/laravel-checker

# Hoặc từ source
git pull origin main
composer install
```

## Gỡ cài đặt

```bash
# Gỡ cài đặt global
composer global remove zteam/laravel-checker

# Gỡ cài đặt local
composer remove zteam/laravel-checker

# Xóa symlink (nếu có)
sudo rm /usr/local/bin/laravel-checker
```

## Hỗ trợ

Nếu gặp vấn đề trong quá trình cài đặt:

1. Kiểm tra [Issues](https://github.com/zteam/laravel-checker/issues)
2. Tạo issue mới với thông tin:
   - Phiên bản PHP: `php --version`
   - Phiên bản Composer: `composer --version`
   - Hệ điều hành
   - Log lỗi đầy đủ

3. Liên hệ: <EMAIL>
