<?php

declare(strict_types=1);

namespace Z<PERSON>eam\<PERSON><PERSON>Checker\Analyzer;

use ZTeam\LaravelChecker\Config\Configuration;

/**
 * Result analyzer
 * 
 * Analyzes scan results and generates summary metrics.
 */
class ResultAnalyzer
{
    private Configuration $config;

    public function __construct(Configuration $config)
    {
        $this->config = $config;
    }

    /**
     * Analyze scan results and generate summary
     */
    public function analyze(array $results): array
    {
        $summary = [
            'overall_score' => 0,
            'quality_score' => 0,
            'security_score' => 0,
            'coverage_score' => 0,
            'dependency_score' => 0,
            'total_files' => 0,
            'total_lines' => 0,
            'code_issues' => 0,
            'security_issues' => 0,
            'outdated_deps' => 0,
            'vulnerable_deps' => 0,
            'test_coverage' => 0,
            'recommendations' => [],
            'release_readiness' => 'unknown'
        ];

        // Analyze quality results
        $summary = $this->analyzeQuality($results['quality'] ?? [], $summary);

        // Analyze security results
        $summary = $this->analyzeSecurity($results['security'] ?? [], $summary);

        // Analyze dependency results
        $summary = $this->analyzeDependencies($results['dependencies'] ?? [], $summary);

        // Analyze coverage results
        $summary = $this->analyzeCoverage($results['coverage'] ?? [], $summary);

        // Calculate overall score
        $summary['overall_score'] = $this->calculateOverallScore($summary);

        // Generate recommendations
        $summary['recommendations'] = $this->generateRecommendations($summary);

        // Determine release readiness
        $summary['release_readiness'] = $this->determineReleaseReadiness($summary);

        return $summary;
    }

    private function analyzeQuality(array $qualityResults, array $summary): array
    {
        // Count total files and lines
        foreach (['php', 'javascript'] as $lang) {
            if (isset($qualityResults[$lang])) {
                $summary['total_files'] += $qualityResults[$lang]['files_scanned'] ?? 0;
                $summary['total_lines'] += $qualityResults[$lang]['lines_of_code'] ?? 0;
                $summary['code_issues'] += count($qualityResults[$lang]['issues'] ?? []);
            }
        }

        // Get quality score
        $summary['quality_score'] = $qualityResults['metrics']['quality_score'] ?? 0;

        return $summary;
    }

    private function analyzeSecurity(array $securityResults, array $summary): array
    {
        // Count security issues
        foreach (['php_vulnerabilities', 'js_vulnerabilities', 'config_vulnerabilities'] as $category) {
            $summary['security_issues'] += count($securityResults[$category] ?? []);
        }

        // Get security score
        $summary['security_score'] = $securityResults['security_score'] ?? 0;

        return $summary;
    }

    private function analyzeDependencies(array $dependencyResults, array $summary): array
    {
        // Count outdated and vulnerable dependencies
        $summary['outdated_deps'] = count($dependencyResults['outdated_packages'] ?? []);
        $summary['vulnerable_deps'] = count($dependencyResults['vulnerable_packages'] ?? []);

        // Calculate dependency score
        $summary['dependency_score'] = $this->calculateDependencyScore($dependencyResults);

        return $summary;
    }

    private function analyzeCoverage(array $coverageResults, array $summary): array
    {
        $summary['test_coverage'] = $coverageResults['coverage_percentage'] ?? 0;
        $summary['coverage_score'] = $this->calculateCoverageScore($coverageResults);

        return $summary;
    }

    private function calculateOverallScore(array $summary): float
    {
        // Weighted average of all scores
        $weights = [
            'quality_score' => 0.3,
            'security_score' => 0.3,
            'coverage_score' => 0.2,
            'dependency_score' => 0.2
        ];

        $totalScore = 0;
        $totalWeight = 0;

        foreach ($weights as $metric => $weight) {
            if (isset($summary[$metric]) && $summary[$metric] > 0) {
                $totalScore += $summary[$metric] * $weight;
                $totalWeight += $weight;
            }
        }

        return $totalWeight > 0 ? $totalScore / $totalWeight : 0;
    }

    private function calculateDependencyScore(array $dependencyResults): float
    {
        $totalDeps = $dependencyResults['total_dependencies'] ?? 0;
        $outdatedDeps = count($dependencyResults['outdated_packages'] ?? []);
        $vulnerableDeps = count($dependencyResults['vulnerable_packages'] ?? []);

        if ($totalDeps === 0) {
            return 100;
        }

        // Calculate score based on percentage of problematic dependencies
        $outdatedPercentage = ($outdatedDeps / $totalDeps) * 100;
        $vulnerablePercentage = ($vulnerableDeps / $totalDeps) * 100;

        $score = 100 - ($outdatedPercentage * 0.5) - ($vulnerablePercentage * 2);

        return max(0, $score);
    }

    private function calculateCoverageScore(array $coverageResults): float
    {
        $coverage = $coverageResults['coverage_percentage'] ?? 0;
        $minCoverage = $this->config->getQualityRules()['min_test_coverage'] ?? 70;

        if ($coverage >= $minCoverage) {
            return 100;
        }

        // Linear scale from 0 to 100 based on minimum coverage requirement
        return ($coverage / $minCoverage) * 100;
    }

    private function generateRecommendations(array $summary): array
    {
        $recommendations = [];

        // Quality recommendations
        if ($summary['quality_score'] < 70) {
            $recommendations[] = [
                'category' => 'quality',
                'priority' => 'high',
                'message' => 'Code quality score is below 70. Consider refactoring complex methods and fixing coding standard violations.',
                'action' => 'Run code quality tools and address identified issues'
            ];
        }

        if ($summary['code_issues'] > 50) {
            $recommendations[] = [
                'category' => 'quality',
                'priority' => 'medium',
                'message' => "Found {$summary['code_issues']} code quality issues. Review and fix the most critical ones.",
                'action' => 'Prioritize fixing error-level issues first'
            ];
        }

        // Security recommendations
        if ($summary['security_issues'] > 0) {
            $priority = $summary['security_issues'] > 10 ? 'critical' : 'high';
            $recommendations[] = [
                'category' => 'security',
                'priority' => $priority,
                'message' => "Found {$summary['security_issues']} security vulnerabilities. Address these before release.",
                'action' => 'Review and fix all security vulnerabilities'
            ];
        }

        // Dependency recommendations
        if ($summary['vulnerable_deps'] > 0) {
            $recommendations[] = [
                'category' => 'dependencies',
                'priority' => 'high',
                'message' => "Found {$summary['vulnerable_deps']} vulnerable dependencies. Update to secure versions.",
                'action' => 'Update vulnerable packages immediately'
            ];
        }

        if ($summary['outdated_deps'] > 5) {
            $recommendations[] = [
                'category' => 'dependencies',
                'priority' => 'medium',
                'message' => "Found {$summary['outdated_deps']} outdated dependencies. Consider updating to latest versions.",
                'action' => 'Plan dependency updates in next development cycle'
            ];
        }

        // Coverage recommendations
        $minCoverage = $this->config->getQualityRules()['min_test_coverage'] ?? 70;
        if ($summary['test_coverage'] < $minCoverage) {
            $recommendations[] = [
                'category' => 'testing',
                'priority' => 'medium',
                'message' => "Test coverage is {$summary['test_coverage']}%, below the minimum requirement of {$minCoverage}%.",
                'action' => 'Write additional tests to improve coverage'
            ];
        }

        return $recommendations;
    }

    private function determineReleaseReadiness(array $summary): string
    {
        // Critical blockers
        if ($summary['security_issues'] > 0) {
            return 'not_ready'; // Security issues are blockers
        }

        if ($summary['vulnerable_deps'] > 0) {
            return 'not_ready'; // Vulnerable dependencies are blockers
        }

        // Quality gates
        $qualityGate = $summary['quality_score'] >= 60;
        $coverageGate = $summary['test_coverage'] >= ($this->config->getQualityRules()['min_test_coverage'] ?? 70);
        $dependencyGate = $summary['outdated_deps'] <= 10;

        $passedGates = array_sum([$qualityGate, $coverageGate, $dependencyGate]);

        if ($passedGates === 3) {
            return 'ready';
        } elseif ($passedGates >= 2) {
            return 'ready_with_warnings';
        } else {
            return 'not_ready';
        }
    }
}
