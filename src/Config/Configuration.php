<?php

declare(strict_types=1);

namespace ZTeam\<PERSON>velChecker\Config;

/**
 * Configuration management for Laravel Checker
 */
class Configuration
{
    private array $config;

    public function __construct(?string $configPath = null)
    {
        $this->config = $this->loadDefaultConfig();
        
        if ($configPath && file_exists($configPath)) {
            $userConfig = json_decode(file_get_contents($configPath), true);
            if ($userConfig) {
                $this->config = array_merge_recursive($this->config, $userConfig);
            }
        }
    }

    private function loadDefaultConfig(): array
    {
        $defaultConfigPath = __DIR__ . '/../../config/default.json';
        
        if (file_exists($defaultConfigPath)) {
            $config = json_decode(file_get_contents($defaultConfigPath), true);
            if ($config) {
                return $config;
            }
        }

        // Fallback default configuration
        return [
            'exclude_dirs' => [
                'vendor',
                'node_modules',
                '.git',
                'storage/logs',
                'storage/cache',
                'bootstrap/cache'
            ],
            'file_extensions' => [
                'php' => ['php'],
                'javascript' => ['js', 'jsx', 'ts', 'tsx'],
                'vue' => ['vue'],
                'css' => ['css', 'scss', 'sass', 'less'],
                'html' => ['html', 'blade.php']
            ],
            'quality_rules' => [
                'max_complexity' => 10,
                'max_method_length' => 50,
                'max_class_length' => 500,
                'min_test_coverage' => 70
            ],
            'security_rules' => [
                'check_sql_injection' => true,
                'check_xss' => true,
                'check_csrf' => true,
                'check_file_upload' => true,
                'check_authentication' => true
            ],
            'dependency_rules' => [
                'check_outdated' => true,
                'check_vulnerabilities' => true,
                'max_outdated_days' => 365
            ]
        ];
    }

    public function get(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    public function getExcludeDirs(): array
    {
        return $this->config['exclude_dirs'] ?? [];
    }

    public function getFileExtensions(): array
    {
        return $this->config['file_extensions'] ?? [];
    }

    public function getQualityRules(): array
    {
        return $this->config['quality_rules'] ?? [];
    }

    public function getSecurityRules(): array
    {
        return $this->config['security_rules'] ?? [];
    }

    public function getDependencyRules(): array
    {
        return $this->config['dependency_rules'] ?? [];
    }

    public function toArray(): array
    {
        return $this->config;
    }
}
