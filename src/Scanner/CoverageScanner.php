<?php

declare(strict_types=1);

namespace ZTeam\LaravelChecker\Scanner;

/**
 * Test coverage scanner
 * 
 * Analyzes test coverage for the project.
 */
class CoverageScanner extends AbstractScanner
{
    public function scan(string $projectPath, array $options = []): array
    {
        $results = [
            'coverage_percentage' => 0,
            'line_coverage' => 0,
            'branch_coverage' => 0,
            'function_coverage' => 0,
            'files_covered' => 0,
            'total_files' => 0,
            'uncovered_files' => [],
            'tools_used' => []
        ];

        // Check for PHPUnit coverage
        $phpunitResults = $this->scanPhpUnitCoverage($projectPath);
        $results = array_merge($results, $phpunitResults);

        // Check for Jest coverage (JavaScript)
        $jestResults = $this->scanJestCoverage($projectPath);
        $results = array_merge($results, $jestResults);

        // Calculate overall coverage
        $results['coverage_percentage'] = $this->calculateOverallCoverage($results);

        return $results;
    }

    private function scanPhpUnitCoverage(string $projectPath): array
    {
        $results = [
            'php_coverage' => [
                'line_coverage' => 0,
                'method_coverage' => 0,
                'class_coverage' => 0,
                'covered_files' => [],
                'uncovered_files' => []
            ]
        ];

        // Check if PHPUnit is available
        if (!$this->commandExists('phpunit') && !file_exists($projectPath . '/vendor/bin/phpunit')) {
            return $results;
        }

        // Look for existing coverage reports
        $coverageReports = [
            $projectPath . '/coverage/clover.xml',
            $projectPath . '/tests/coverage/clover.xml',
            $projectPath . '/build/coverage/clover.xml'
        ];

        $coverageFound = false;
        foreach ($coverageReports as $reportPath) {
            if (file_exists($reportPath)) {
                $results['php_coverage'] = $this->parseCloverReport($reportPath);
                $coverageFound = true;
                $results['tools_used'][] = 'phpunit-clover';
                break;
            }
        }

        // If no existing coverage report, try to generate one
        if (!$coverageFound) {
            $generatedCoverage = $this->generatePhpUnitCoverage($projectPath);
            if ($generatedCoverage) {
                $results['php_coverage'] = $generatedCoverage;
                $results['tools_used'][] = 'phpunit-generated';
            }
        }

        return $results;
    }

    private function generatePhpUnitCoverage(string $projectPath): ?array
    {
        // Check if phpunit.xml exists
        $phpunitConfig = $projectPath . '/phpunit.xml';
        if (!file_exists($phpunitConfig)) {
            return null;
        }

        // Create temporary coverage directory
        $tempCoverageDir = $projectPath . '/temp_coverage';
        if (!is_dir($tempCoverageDir)) {
            mkdir($tempCoverageDir, 0755, true);
        }

        $cloverPath = $tempCoverageDir . '/clover.xml';

        // Run PHPUnit with coverage
        $phpunitBinary = file_exists($projectPath . '/vendor/bin/phpunit') 
            ? $projectPath . '/vendor/bin/phpunit'
            : 'phpunit';

        $command = [
            $phpunitBinary,
            '--coverage-clover=' . $cloverPath,
            '--configuration=' . $phpunitConfig
        ];

        $result = $this->executeCommand($command, $projectPath);

        if ($result['success'] && file_exists($cloverPath)) {
            $coverage = $this->parseCloverReport($cloverPath);
            
            // Clean up temporary files
            unlink($cloverPath);
            rmdir($tempCoverageDir);
            
            return $coverage;
        }

        // Clean up on failure
        if (is_dir($tempCoverageDir)) {
            if (file_exists($cloverPath)) {
                unlink($cloverPath);
            }
            rmdir($tempCoverageDir);
        }

        return null;
    }

    private function parseCloverReport(string $cloverPath): array
    {
        $coverage = [
            'line_coverage' => 0,
            'method_coverage' => 0,
            'class_coverage' => 0,
            'covered_files' => [],
            'uncovered_files' => []
        ];

        if (!file_exists($cloverPath)) {
            return $coverage;
        }

        $xml = simplexml_load_file($cloverPath);
        if (!$xml) {
            return $coverage;
        }

        // Parse project metrics
        $project = $xml->project;
        if ($project && $project->metrics) {
            $metrics = $project->metrics;
            
            $totalLines = (int)$metrics['statements'];
            $coveredLines = (int)$metrics['coveredstatements'];
            $totalMethods = (int)$metrics['methods'];
            $coveredMethods = (int)$metrics['coveredmethods'];
            $totalClasses = (int)$metrics['classes'];
            $coveredClasses = (int)$metrics['coveredclasses'];

            $coverage['line_coverage'] = $totalLines > 0 ? ($coveredLines / $totalLines) * 100 : 0;
            $coverage['method_coverage'] = $totalMethods > 0 ? ($coveredMethods / $totalMethods) * 100 : 0;
            $coverage['class_coverage'] = $totalClasses > 0 ? ($coveredClasses / $totalClasses) * 100 : 0;
        }

        // Parse file-level coverage
        foreach ($xml->xpath('//file') as $file) {
            $fileName = (string)$file['name'];
            $fileMetrics = $file->metrics;
            
            if ($fileMetrics) {
                $totalLines = (int)$fileMetrics['statements'];
                $coveredLines = (int)$fileMetrics['coveredstatements'];
                $fileCoverage = $totalLines > 0 ? ($coveredLines / $totalLines) * 100 : 0;

                $fileInfo = [
                    'file' => $fileName,
                    'coverage' => $fileCoverage,
                    'lines_total' => $totalLines,
                    'lines_covered' => $coveredLines
                ];

                if ($fileCoverage > 0) {
                    $coverage['covered_files'][] = $fileInfo;
                } else {
                    $coverage['uncovered_files'][] = $fileInfo;
                }
            }
        }

        return $coverage;
    }

    private function scanJestCoverage(string $projectPath): array
    {
        $results = [
            'js_coverage' => [
                'line_coverage' => 0,
                'branch_coverage' => 0,
                'function_coverage' => 0,
                'statement_coverage' => 0,
                'covered_files' => [],
                'uncovered_files' => []
            ]
        ];

        // Check if Jest is available
        if (!$this->commandExists('jest') && !file_exists($projectPath . '/node_modules/.bin/jest')) {
            return $results;
        }

        // Look for existing Jest coverage reports
        $coverageReports = [
            $projectPath . '/coverage/coverage-summary.json',
            $projectPath . '/tests/coverage/coverage-summary.json'
        ];

        $coverageFound = false;
        foreach ($coverageReports as $reportPath) {
            if (file_exists($reportPath)) {
                $results['js_coverage'] = $this->parseJestCoverageReport($reportPath);
                $coverageFound = true;
                $results['tools_used'][] = 'jest-coverage';
                break;
            }
        }

        // If no existing coverage report, try to generate one
        if (!$coverageFound) {
            $generatedCoverage = $this->generateJestCoverage($projectPath);
            if ($generatedCoverage) {
                $results['js_coverage'] = $generatedCoverage;
                $results['tools_used'][] = 'jest-generated';
            }
        }

        return $results;
    }

    private function generateJestCoverage(string $projectPath): ?array
    {
        // Check if package.json has Jest configuration
        $packageJsonPath = $projectPath . '/package.json';
        if (!file_exists($packageJsonPath)) {
            return null;
        }

        $jestBinary = file_exists($projectPath . '/node_modules/.bin/jest')
            ? $projectPath . '/node_modules/.bin/jest'
            : 'jest';

        $command = [
            $jestBinary,
            '--coverage',
            '--coverageReporters=json-summary',
            '--passWithNoTests'
        ];

        $result = $this->executeCommand($command, $projectPath);

        $coveragePath = $projectPath . '/coverage/coverage-summary.json';
        if ($result['success'] && file_exists($coveragePath)) {
            return $this->parseJestCoverageReport($coveragePath);
        }

        return null;
    }

    private function parseJestCoverageReport(string $reportPath): array
    {
        $coverage = [
            'line_coverage' => 0,
            'branch_coverage' => 0,
            'function_coverage' => 0,
            'statement_coverage' => 0,
            'covered_files' => [],
            'uncovered_files' => []
        ];

        if (!file_exists($reportPath)) {
            return $coverage;
        }

        $data = json_decode(file_get_contents($reportPath), true);
        if (!$data) {
            return $coverage;
        }

        // Parse total coverage
        if (isset($data['total'])) {
            $total = $data['total'];
            $coverage['line_coverage'] = $total['lines']['pct'] ?? 0;
            $coverage['branch_coverage'] = $total['branches']['pct'] ?? 0;
            $coverage['function_coverage'] = $total['functions']['pct'] ?? 0;
            $coverage['statement_coverage'] = $total['statements']['pct'] ?? 0;
        }

        // Parse file-level coverage
        foreach ($data as $fileName => $fileData) {
            if ($fileName === 'total') {
                continue;
            }

            $fileCoverage = $fileData['lines']['pct'] ?? 0;
            $fileInfo = [
                'file' => $fileName,
                'coverage' => $fileCoverage,
                'lines' => $fileData['lines'] ?? [],
                'branches' => $fileData['branches'] ?? [],
                'functions' => $fileData['functions'] ?? [],
                'statements' => $fileData['statements'] ?? []
            ];

            if ($fileCoverage > 0) {
                $coverage['covered_files'][] = $fileInfo;
            } else {
                $coverage['uncovered_files'][] = $fileInfo;
            }
        }

        return $coverage;
    }

    private function calculateOverallCoverage(array $results): float
    {
        $phpCoverage = $results['php_coverage']['line_coverage'] ?? 0;
        $jsCoverage = $results['js_coverage']['line_coverage'] ?? 0;

        // If both exist, take weighted average
        if ($phpCoverage > 0 && $jsCoverage > 0) {
            return ($phpCoverage + $jsCoverage) / 2;
        }

        // Return whichever exists
        return $phpCoverage > 0 ? $phpCoverage : $jsCoverage;
    }
}
