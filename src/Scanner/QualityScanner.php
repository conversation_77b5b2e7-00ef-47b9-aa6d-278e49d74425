<?php

declare(strict_types=1);

namespace Z<PERSON>ea<PERSON>\LaravelChecker\Scanner;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use PhpParser\NodeFinder;
use Php<PERSON><PERSON><PERSON>\ParserFactory;
use Php<PERSON><PERSON><PERSON>\Parser;
use <PERSON>pParser\Node;

/**
 * Code quality scanner
 * 
 * Analyzes code quality metrics including complexity, code smells, and standards compliance.
 */
class QualityScanner extends AbstractScanner
{
    public function scan(string $projectPath, array $options = []): array
    {
        $results = [
            'total_files' => 0,
            'total_lines' => 0,
            'issues' => [],
            'metrics' => [],
            'tools_used' => []
        ];

        // Check if PhpParser is available
        $phpParserAvailable = $this->isPhpParserAvailable();
        if (!$phpParserAvailable) {
            $results['warnings'][] = 'PhpParser not available - advanced PHP analysis disabled';
        }

        // Scan PHP files
        $phpResults = $this->scanPhpFiles($projectPath, $options);
        $results = array_merge_recursive($results, $phpResults);

        // Scan JavaScript/Vue files
        $jsResults = $this->scanJavaScriptFiles($projectPath, $options);
        $results = array_merge_recursive($results, $jsResults);

        // Calculate overall metrics
        $results['metrics']['quality_score'] = $this->calculateQualityScore($results);

        return $results;
    }

    private function scanPhpFiles(string $projectPath, array $options): array
    {
        $results = [
            'php' => [
                'files_scanned' => 0,
                'lines_of_code' => 0,
                'issues' => [],
                'complexity' => []
            ]
        ];

        $phpExtensions = $this->config->getFileExtensions()['php'] ?? ['php'];
        $files = $this->getFilesToScan($projectPath, $phpExtensions, $options['exclude_dirs'] ?? []);

        foreach ($files as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $this->getRelativePath($filePath, $projectPath);
            
            $results['php']['files_scanned']++;
            $results['php']['lines_of_code'] += $this->countLinesOfCode($filePath);

            // Parse PHP file for complexity analysis
            $fileIssues = $this->analyzePhpFile($filePath, $relativePath);
            $results['php']['issues'] = array_merge($results['php']['issues'], $fileIssues);
        }

        // Run external tools
        $results['php'] = array_merge($results['php'], $this->runPhpTools($projectPath));

        return $results;
    }

    private function analyzePhpFile(string $filePath, string $relativePath): array
    {
        $issues = [];

        try {
            $code = file_get_contents($filePath);

            // Check if PhpParser is available
            if (!$this->isPhpParserAvailable()) {
                // Fallback to basic syntax check
                return $this->basicPhpSyntaxCheck($code, $relativePath);
            }

            // Create parser with compatibility for different versions
            $parser = $this->createParser();
            $ast = $parser->parse($code);

            if ($ast) {
                $issues = array_merge($issues, $this->checkComplexity($ast, $relativePath));
                $issues = array_merge($issues, $this->checkMethodLength($ast, $relativePath));
                $issues = array_merge($issues, $this->checkClassLength($ast, $relativePath));
            }

        } catch (Error $e) {
            $issues[] = [
                'type' => 'syntax_error',
                'severity' => 'error',
                'file' => $relativePath,
                'line' => $e->getStartLine(),
                'message' => 'PHP Parse Error: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            $issues[] = [
                'type' => 'parse_error',
                'severity' => 'error',
                'file' => $relativePath,
                'line' => 1,
                'message' => 'Failed to parse file: ' . $e->getMessage()
            ];
        }

        return $issues;
    }

    /**
     * Basic PHP syntax check without PhpParser
     */
    private function basicPhpSyntaxCheck(string $code, string $relativePath): array
    {
        $issues = [];

        // Create temporary file for syntax check
        $tempFile = tempnam(sys_get_temp_dir(), 'php_syntax_check');
        file_put_contents($tempFile, $code);

        // Run php -l to check syntax
        $output = [];
        $returnCode = 0;
        exec("php -l " . escapeshellarg($tempFile) . " 2>&1", $output, $returnCode);

        // Clean up temp file
        unlink($tempFile);

        if ($returnCode !== 0) {
            // Parse error output to extract line number
            $errorMessage = implode("\n", $output);
            $lineNumber = 1;

            if (preg_match('/on line (\d+)/', $errorMessage, $matches)) {
                $lineNumber = (int)$matches[1];
            }

            $issues[] = [
                'type' => 'syntax_error',
                'severity' => 'error',
                'file' => $relativePath,
                'line' => $lineNumber,
                'message' => 'PHP Syntax Error: ' . trim($errorMessage)
            ];
        }

        return $issues;
    }

    /**
     * Check if PhpParser is available and working
     */
    private function isPhpParserAvailable(): bool
    {
        try {
            if (!class_exists('PhpParser\ParserFactory')) {
                return false;
            }

            $parser = $this->createParser();
            return $parser !== null;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Create PHP parser with version compatibility
     */
    private function createParser(): Parser
    {
        $factory = new ParserFactory();

        // Try different methods based on PhpParser version
        if (method_exists($factory, 'createForNewestSupportedVersion')) {
            // PhpParser 5.x
            return $factory->createForNewestSupportedVersion();
        } elseif (method_exists($factory, 'createForHostVersion')) {
            // PhpParser 4.x
            return $factory->createForHostVersion();
        } elseif (method_exists($factory, 'create')) {
            // PhpParser 4.x older versions
            if (defined('PhpParser\ParserFactory::PREFER_PHP7')) {
                return $factory->create(\PhpParser\ParserFactory::PREFER_PHP7);
            } elseif (defined('PhpParser\ParserFactory::ONLY_PHP7')) {
                return $factory->create(\PhpParser\ParserFactory::ONLY_PHP7);
            } else {
                // Try with no arguments
                return $factory->create();
            }
        } else {
            // Fallback - create basic parser
            throw new \RuntimeException('Unsupported PhpParser version. Please install nikic/php-parser ^4.15 or ^5.0');
        }
    }

    private function checkComplexity(array $ast, string $relativePath): array
    {
        $issues = [];
        $maxComplexity = $this->config->getQualityRules()['max_complexity'] ?? 10;
        
        $nodeFinder = new NodeFinder;
        $methods = $nodeFinder->findInstanceOf($ast, Node\Stmt\ClassMethod::class);
        $functions = $nodeFinder->findInstanceOf($ast, Node\Stmt\Function_::class);

        foreach (array_merge($methods, $functions) as $node) {
            $complexity = $this->calculateCyclomaticComplexity($node);
            
            if ($complexity > $maxComplexity) {
                $issues[] = [
                    'type' => 'high_complexity',
                    'severity' => 'warning',
                    'file' => $relativePath,
                    'line' => $node->getStartLine(),
                    'message' => "Method/Function has high cyclomatic complexity: {$complexity} (max: {$maxComplexity})",
                    'complexity' => $complexity
                ];
            }
        }

        return $issues;
    }

    private function calculateCyclomaticComplexity(Node $node): int
    {
        $complexity = 1; // Base complexity
        
        $nodeFinder = new NodeFinder;
        
        // Count decision points
        $decisionNodes = [
            Node\Stmt\If_::class,
            Node\Stmt\ElseIf_::class,
            Node\Stmt\While_::class,
            Node\Stmt\For_::class,
            Node\Stmt\Foreach_::class,
            Node\Stmt\Switch_::class,
            Node\Stmt\Case_::class,
            Node\Stmt\Catch_::class,
            Node\Expr\Ternary::class,
            Node\Expr\BinaryOp\BooleanAnd::class,
            Node\Expr\BinaryOp\BooleanOr::class,
        ];

        foreach ($decisionNodes as $nodeClass) {
            $nodes = $nodeFinder->findInstanceOf([$node], $nodeClass);
            $complexity += count($nodes);
        }

        return $complexity;
    }

    private function checkMethodLength(array $ast, string $relativePath): array
    {
        $issues = [];
        $maxLength = $this->config->getQualityRules()['max_method_length'] ?? 50;
        
        $nodeFinder = new NodeFinder;
        $methods = $nodeFinder->findInstanceOf($ast, Node\Stmt\ClassMethod::class);

        foreach ($methods as $method) {
            $length = $method->getEndLine() - $method->getStartLine() + 1;
            
            if ($length > $maxLength) {
                $issues[] = [
                    'type' => 'long_method',
                    'severity' => 'warning',
                    'file' => $relativePath,
                    'line' => $method->getStartLine(),
                    'message' => "Method is too long: {$length} lines (max: {$maxLength})",
                    'length' => $length
                ];
            }
        }

        return $issues;
    }

    private function checkClassLength(array $ast, string $relativePath): array
    {
        $issues = [];
        $maxLength = $this->config->getQualityRules()['max_class_length'] ?? 500;
        
        $nodeFinder = new NodeFinder;
        $classes = $nodeFinder->findInstanceOf($ast, Node\Stmt\Class_::class);

        foreach ($classes as $class) {
            $length = $class->getEndLine() - $class->getStartLine() + 1;
            
            if ($length > $maxLength) {
                $issues[] = [
                    'type' => 'long_class',
                    'severity' => 'warning',
                    'file' => $relativePath,
                    'line' => $class->getStartLine(),
                    'message' => "Class is too long: {$length} lines (max: {$maxLength})",
                    'length' => $length
                ];
            }
        }

        return $issues;
    }

    private function runPhpTools(string $projectPath): array
    {
        $results = ['tools_used' => []];

        // Run PHP_CodeSniffer
        if ($this->commandExists('phpcs')) {
            $phpcsResult = $this->runPhpCs($projectPath);
            $results['tools_used'][] = 'phpcs';
            $results['issues'] = array_merge($results['issues'] ?? [], $phpcsResult);
        }

        // Run PHPStan
        if ($this->commandExists('phpstan')) {
            $phpstanResult = $this->runPhpStan($projectPath);
            $results['tools_used'][] = 'phpstan';
            $results['issues'] = array_merge($results['issues'] ?? [], $phpstanResult);
        }

        return $results;
    }

    private function runPhpCs(string $projectPath): array
    {
        $standard = $this->config->get('tools')['phpcs']['standard'] ?? 'PSR12';
        $command = ['phpcs', '--standard=' . $standard, '--report=json', $projectPath];
        
        $result = $this->executeCommand($command, $projectPath);
        $issues = [];

        if (!empty($result['output'])) {
            $data = $this->parseJsonOutput($result['output']);
            
            foreach ($data['files'] ?? [] as $file => $fileData) {
                $relativePath = $this->getRelativePath($file, $projectPath);
                
                foreach ($fileData['messages'] ?? [] as $message) {
                    $issues[] = [
                        'type' => 'coding_standard',
                        'severity' => strtolower($message['type']),
                        'file' => $relativePath,
                        'line' => $message['line'],
                        'column' => $message['column'],
                        'message' => $message['message'],
                        'source' => $message['source'] ?? 'phpcs'
                    ];
                }
            }
        }

        return $issues;
    }

    private function runPhpStan(string $projectPath): array
    {
        $level = $this->config->get('tools')['phpstan']['level'] ?? 6;
        $command = ['phpstan', 'analyse', '--level=' . $level, '--error-format=json', $projectPath];
        
        $result = $this->executeCommand($command, $projectPath);
        $issues = [];

        if (!empty($result['output'])) {
            $data = $this->parseJsonOutput($result['output']);
            
            foreach ($data['files'] ?? [] as $file => $fileData) {
                $relativePath = $this->getRelativePath($file, $projectPath);
                
                foreach ($fileData['messages'] ?? [] as $message) {
                    $issues[] = [
                        'type' => 'static_analysis',
                        'severity' => 'warning',
                        'file' => $relativePath,
                        'line' => $message['line'],
                        'message' => $message['message'],
                        'source' => 'phpstan'
                    ];
                }
            }
        }

        return $issues;
    }

    private function scanJavaScriptFiles(string $projectPath, array $options): array
    {
        $results = [
            'javascript' => [
                'files_scanned' => 0,
                'lines_of_code' => 0,
                'issues' => []
            ]
        ];

        $jsExtensions = array_merge(
            $this->config->getFileExtensions()['javascript'] ?? [],
            $this->config->getFileExtensions()['vue'] ?? []
        );

        if (empty($jsExtensions)) {
            return $results;
        }

        $files = $this->getFilesToScan($projectPath, $jsExtensions, $options['exclude_dirs'] ?? []);

        foreach ($files as $file) {
            $filePath = $file->getRealPath();
            $results['javascript']['files_scanned']++;
            $results['javascript']['lines_of_code'] += $this->countLinesOfCode($filePath);
        }

        // Run ESLint if available
        if ($this->commandExists('eslint')) {
            $eslintResult = $this->runEsLint($projectPath);
            $results['javascript']['issues'] = array_merge($results['javascript']['issues'], $eslintResult);
            $results['tools_used'][] = 'eslint';
        }

        return $results;
    }

    private function runEsLint(string $projectPath): array
    {
        $command = ['eslint', '--format=json', $projectPath];
        
        $result = $this->executeCommand($command, $projectPath);
        $issues = [];

        if (!empty($result['output'])) {
            $data = $this->parseJsonOutput($result['output']);
            
            foreach ($data as $fileData) {
                $relativePath = $this->getRelativePath($fileData['filePath'], $projectPath);
                
                foreach ($fileData['messages'] ?? [] as $message) {
                    $issues[] = [
                        'type' => 'eslint',
                        'severity' => $message['severity'] == 2 ? 'error' : 'warning',
                        'file' => $relativePath,
                        'line' => $message['line'],
                        'column' => $message['column'],
                        'message' => $message['message'],
                        'rule' => $message['ruleId'] ?? 'unknown'
                    ];
                }
            }
        }

        return $issues;
    }

    private function calculateQualityScore(array $results): float
    {
        $totalIssues = 0;
        $totalFiles = 0;
        $totalLines = 0;

        // Count issues and files
        foreach (['php', 'javascript'] as $lang) {
            if (isset($results[$lang])) {
                $totalIssues += count($results[$lang]['issues'] ?? []);
                $totalFiles += $results[$lang]['files_scanned'] ?? 0;
                $totalLines += $results[$lang]['lines_of_code'] ?? 0;
            }
        }

        if ($totalFiles === 0) {
            return 0;
        }

        // Calculate score based on issues per file and lines
        $issuesPerFile = $totalFiles > 0 ? $totalIssues / $totalFiles : 0;
        $issuesPerKLOC = $totalLines > 0 ? ($totalIssues / $totalLines) * 1000 : 0;

        // Score calculation (0-100)
        $score = 100 - $issuesPerFile * 10 - $issuesPerKLOC * 5;
        
        return max(0, min(100, $score));
    }
}
