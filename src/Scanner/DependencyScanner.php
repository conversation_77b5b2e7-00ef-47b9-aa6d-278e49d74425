<?php

declare(strict_types=1);

namespace ZTeam\LaravelChecker\Scanner;

/**
 * Dependency scanner
 * 
 * Scans for outdated dependencies and known vulnerabilities in packages.
 */
class DependencyScanner extends AbstractScanner
{
    public function scan(string $projectPath, array $options = []): array
    {
        $results = [
            'composer' => [],
            'npm' => [],
            'outdated_packages' => [],
            'vulnerable_packages' => [],
            'total_dependencies' => 0,
            'tools_used' => []
        ];

        // Scan Composer dependencies
        $composerResults = $this->scanComposerDependencies($projectPath);
        $results = array_merge_recursive($results, $composerResults);

        // Scan NPM dependencies
        $npmResults = $this->scanNpmDependencies($projectPath);
        $results = array_merge_recursive($results, $npmResults);

        return $results;
    }

    private function scanComposerDependencies(string $projectPath): array
    {
        $results = [
            'composer' => [
                'dependencies' => [],
                'outdated' => [],
                'vulnerabilities' => []
            ]
        ];

        $composerJsonPath = $projectPath . '/composer.json';
        $composerLockPath = $projectPath . '/composer.lock';

        if (!file_exists($composerJsonPath)) {
            return $results;
        }

        // Parse composer.json
        $composerJson = json_decode(file_get_contents($composerJsonPath), true);
        if (!$composerJson) {
            return $results;
        }

        // Get all dependencies
        $dependencies = array_merge(
            $composerJson['require'] ?? [],
            $composerJson['require-dev'] ?? []
        );

        $results['composer']['dependencies'] = $dependencies;
        $results['total_dependencies'] += count($dependencies);

        // Check for outdated packages
        if ($this->commandExists('composer')) {
            $outdatedResult = $this->checkComposerOutdated($projectPath);
            $results['composer']['outdated'] = $outdatedResult;
            $results['outdated_packages'] = array_merge($results['outdated_packages'], $outdatedResult);
            $results['tools_used'][] = 'composer';
        }

        // Check for security vulnerabilities
        $vulnerabilityResult = $this->checkComposerVulnerabilities($projectPath);
        $results['composer']['vulnerabilities'] = $vulnerabilityResult;
        $results['vulnerable_packages'] = array_merge($results['vulnerable_packages'], $vulnerabilityResult);

        return $results;
    }

    private function checkComposerOutdated(string $projectPath): array
    {
        $command = ['composer', 'outdated', '--format=json', '--direct'];
        $result = $this->executeCommand($command, $projectPath);
        
        $outdated = [];
        if ($result['success'] && !empty($result['output'])) {
            $data = $this->parseJsonOutput($result['output']);
            
            foreach ($data['installed'] ?? [] as $package) {
                if (isset($package['latest']) && $package['version'] !== $package['latest']) {
                    $outdated[] = [
                        'name' => $package['name'],
                        'current_version' => $package['version'],
                        'latest_version' => $package['latest'],
                        'type' => 'composer',
                        'age_days' => $this->calculatePackageAge($package['version'], $package['latest'])
                    ];
                }
            }
        }

        return $outdated;
    }

    private function checkComposerVulnerabilities(string $projectPath): array
    {
        $vulnerabilities = [];

        // Try using composer audit (Composer 2.4+)
        if ($this->commandExists('composer')) {
            $command = ['composer', 'audit', '--format=json'];
            $result = $this->executeCommand($command, $projectPath);
            
            if ($result['success'] && !empty($result['output'])) {
                $data = $this->parseJsonOutput($result['output']);
                
                foreach ($data['advisories'] ?? [] as $advisory) {
                    $vulnerabilities[] = [
                        'package' => $advisory['packageName'],
                        'version' => $advisory['affectedVersions'],
                        'vulnerability' => $advisory['title'],
                        'severity' => $advisory['severity'] ?? 'unknown',
                        'cve' => $advisory['cve'] ?? null,
                        'link' => $advisory['link'] ?? null,
                        'type' => 'composer'
                    ];
                }
            }
        }

        return $vulnerabilities;
    }

    private function scanNpmDependencies(string $projectPath): array
    {
        $results = [
            'npm' => [
                'dependencies' => [],
                'outdated' => [],
                'vulnerabilities' => []
            ]
        ];

        $packageJsonPath = $projectPath . '/package.json';
        $packageLockPath = $projectPath . '/package-lock.json';

        if (!file_exists($packageJsonPath)) {
            return $results;
        }

        // Parse package.json
        $packageJson = json_decode(file_get_contents($packageJsonPath), true);
        if (!$packageJson) {
            return $results;
        }

        // Get all dependencies
        $dependencies = array_merge(
            $packageJson['dependencies'] ?? [],
            $packageJson['devDependencies'] ?? []
        );

        $results['npm']['dependencies'] = $dependencies;
        $results['total_dependencies'] += count($dependencies);

        // Check for outdated packages
        if ($this->commandExists('npm')) {
            $outdatedResult = $this->checkNpmOutdated($projectPath);
            $results['npm']['outdated'] = $outdatedResult;
            $results['outdated_packages'] = array_merge($results['outdated_packages'], $outdatedResult);
            $results['tools_used'][] = 'npm';
        }

        // Check for security vulnerabilities
        $vulnerabilityResult = $this->checkNpmVulnerabilities($projectPath);
        $results['npm']['vulnerabilities'] = $vulnerabilityResult;
        $results['vulnerable_packages'] = array_merge($results['vulnerable_packages'], $vulnerabilityResult);

        return $results;
    }

    private function checkNpmOutdated(string $projectPath): array
    {
        $command = ['npm', 'outdated', '--json'];
        $result = $this->executeCommand($command, $projectPath);
        
        $outdated = [];
        if (!empty($result['output'])) {
            $data = $this->parseJsonOutput($result['output']);
            
            foreach ($data as $packageName => $packageInfo) {
                $outdated[] = [
                    'name' => $packageName,
                    'current_version' => $packageInfo['current'],
                    'latest_version' => $packageInfo['latest'],
                    'wanted_version' => $packageInfo['wanted'],
                    'type' => 'npm',
                    'age_days' => $this->calculatePackageAge($packageInfo['current'], $packageInfo['latest'])
                ];
            }
        }

        return $outdated;
    }

    private function checkNpmVulnerabilities(string $projectPath): array
    {
        $command = ['npm', 'audit', '--json'];
        $result = $this->executeCommand($command, $projectPath);
        
        $vulnerabilities = [];
        if (!empty($result['output'])) {
            $data = $this->parseJsonOutput($result['output']);
            
            foreach ($data['vulnerabilities'] ?? [] as $vulnId => $vulnerability) {
                $vulnerabilities[] = [
                    'package' => $vulnerability['name'],
                    'version' => $vulnerability['range'],
                    'vulnerability' => $vulnerability['title'],
                    'severity' => $vulnerability['severity'],
                    'cve' => $vulnerability['cves'][0] ?? null,
                    'link' => $vulnerability['url'] ?? null,
                    'type' => 'npm'
                ];
            }
        }

        return $vulnerabilities;
    }

    private function calculatePackageAge(string $currentVersion, string $latestVersion): int
    {
        // This is a simplified calculation
        // In a real implementation, you might want to use package registry APIs
        // to get actual release dates
        
        if (version_compare($currentVersion, $latestVersion, '>=')) {
            return 0;
        }

        // Estimate based on version difference
        $currentParts = explode('.', $currentVersion);
        $latestParts = explode('.', $latestVersion);

        $majorDiff = (int)($latestParts[0] ?? 0) - (int)($currentParts[0] ?? 0);
        $minorDiff = (int)($latestParts[1] ?? 0) - (int)($currentParts[1] ?? 0);
        $patchDiff = (int)($latestParts[2] ?? 0) - (int)($currentParts[2] ?? 0);

        // Rough estimation: major = 365 days, minor = 90 days, patch = 30 days
        return ($majorDiff * 365) + ($minorDiff * 90) + ($patchDiff * 30);
    }
}
