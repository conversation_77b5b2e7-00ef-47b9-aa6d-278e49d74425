<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ea<PERSON>\LaravelChecker\Scanner;

use Symfony\Component\Finder\Finder;
use Symfony\Component\Process\Process;
use ZTeam\LaravelChecker\Config\Configuration;

/**
 * Abstract base class for all scanners
 */
abstract class AbstractScanner
{
    protected Configuration $config;

    public function __construct(Configuration $config)
    {
        $this->config = $config;
    }

    /**
     * Scan the project directory
     */
    abstract public function scan(string $projectPath, array $options = []): array;

    /**
     * Get files to scan based on extensions
     */
    protected function getFilesToScan(string $projectPath, array $extensions, array $excludeDirs = []): Finder
    {
        $finder = new Finder();
        $finder->files()
            ->in($projectPath)
            ->name('/\.(' . implode('|', $extensions) . ')$/')
            ->ignoreVCS(true)
            ->ignoreUnreadableDirs();

        // Exclude directories
        $allExcludeDirs = array_merge($this->config->getExcludeDirs(), $excludeDirs);
        foreach ($allExcludeDirs as $dir) {
            $finder->exclude($dir);
        }

        return $finder;
    }

    /**
     * Execute a command and return the result
     */
    protected function executeCommand(array $command, string $workingDir = null): array
    {
        $process = new Process($command, $workingDir);
        $process->setTimeout(300); // 5 minutes timeout
        $process->run();

        return [
            'exit_code' => $process->getExitCode(),
            'output' => $process->getOutput(),
            'error' => $process->getErrorOutput(),
            'success' => $process->isSuccessful()
        ];
    }

    /**
     * Check if a command exists in the system
     */
    protected function commandExists(string $command): bool
    {
        $process = new Process(['which', $command]);
        $process->run();
        return $process->isSuccessful();
    }

    /**
     * Parse JSON output safely
     */
    protected function parseJsonOutput(string $output): array
    {
        $decoded = json_decode($output, true);
        return $decoded ?? [];
    }

    /**
     * Count lines of code in a file
     */
    protected function countLinesOfCode(string $filePath): int
    {
        if (!file_exists($filePath)) {
            return 0;
        }

        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        
        // Count non-empty, non-comment lines
        $loc = 0;
        foreach ($lines as $line) {
            $trimmed = trim($line);
            if (!empty($trimmed) && !$this->isCommentLine($trimmed)) {
                $loc++;
            }
        }

        return $loc;
    }

    /**
     * Check if a line is a comment
     */
    protected function isCommentLine(string $line): bool
    {
        $line = trim($line);
        
        // PHP comments
        if (str_starts_with($line, '//') || str_starts_with($line, '#') || 
            str_starts_with($line, '/*') || str_starts_with($line, '*')) {
            return true;
        }

        // JavaScript comments
        if (str_starts_with($line, '//') || str_starts_with($line, '/*')) {
            return true;
        }

        // HTML comments
        if (str_starts_with($line, '<!--')) {
            return true;
        }

        return false;
    }

    /**
     * Get relative path from project root
     */
    protected function getRelativePath(string $filePath, string $projectPath): string
    {
        return str_replace($projectPath . '/', '', $filePath);
    }
}
