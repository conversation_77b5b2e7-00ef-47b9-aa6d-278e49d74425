<?php

declare(strict_types=1);

namespace ZTeam\<PERSON>velChecker\Scanner;

/**
 * Security vulnerability scanner
 * 
 * Scans for common security vulnerabilities like SQL injection, XSS, CSRF, etc.
 */
class SecurityScanner extends AbstractScanner
{
    private array $securityPatterns;

    public function __construct($config)
    {
        parent::__construct($config);
        $this->initializeSecurityPatterns();
    }

    public function scan(string $projectPath, array $options = []): array
    {
        $results = [
            'vulnerabilities' => [],
            'security_score' => 0,
            'files_scanned' => 0,
            'tools_used' => []
        ];

        // Scan PHP files for security issues
        $phpResults = $this->scanPhpSecurity($projectPath, $options);
        $results = array_merge_recursive($results, $phpResults);

        // Scan JavaScript files for security issues
        $jsResults = $this->scanJavaScriptSecurity($projectPath, $options);
        $results = array_merge_recursive($results, $jsResults);

        // Check configuration files
        $configResults = $this->scanConfigurationSecurity($projectPath, $options);
        $results = array_merge_recursive($results, $configResults);

        // Calculate security score
        $results['security_score'] = $this->calculateSecurityScore($results);

        return $results;
    }

    private function initializeSecurityPatterns(): void
    {
        $this->securityPatterns = [
            'sql_injection' => [
                '/\$_(?:GET|POST|REQUEST|COOKIE)\s*\[\s*[\'"][^\'"]*[\'"]\s*\]\s*(?:(?:\.|\+|,)\s*)*(?:(?:SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\s+)/i',
                '/DB::raw\s*\(\s*[\'"][^\'"]*\$[^\'"]*[\'"]\s*\)/i',
                '/query\s*\(\s*[\'"][^\'"]*\$[^\'"]*[\'"]\s*\)/i',
                '/mysqli_query\s*\([^,]+,\s*[\'"][^\'"]*\$[^\'"]*[\'"]\s*\)/i'
            ],
            'xss' => [
                '/echo\s+\$_(?:GET|POST|REQUEST|COOKIE)/i',
                '/print\s+\$_(?:GET|POST|REQUEST|COOKIE)/i',
                '/\{\{\s*\$[^}]*\s*\}\}/i', // Laravel Blade without escaping
                '/innerHTML\s*=\s*[^;]*\$_/i'
            ],
            'file_inclusion' => [
                '/(?:include|require)(?:_once)?\s*\(\s*\$_(?:GET|POST|REQUEST|COOKIE)/i',
                '/file_get_contents\s*\(\s*\$_(?:GET|POST|REQUEST|COOKIE)/i',
                '/fopen\s*\(\s*\$_(?:GET|POST|REQUEST|COOKIE)/i'
            ],
            'command_injection' => [
                '/(?:exec|system|shell_exec|passthru|popen|proc_open)\s*\([^)]*\$_(?:GET|POST|REQUEST|COOKIE)/i',
                '/`[^`]*\$_(?:GET|POST|REQUEST|COOKIE)[^`]*`/i'
            ],
            'file_upload' => [
                '/move_uploaded_file\s*\([^,]+,\s*[^)]*\$_(?:GET|POST|REQUEST|COOKIE)/i',
                '/\$_FILES\[[^\]]+\]\[[\'"]tmp_name[\'"]\]/i'
            ],
            'hardcoded_secrets' => [
                '/(?:password|secret|key|token)\s*=\s*[\'"][^\'"]{8,}[\'"]/i',
                '/(?:api_key|api_secret|access_token)\s*=\s*[\'"][^\'"]+[\'"]/i'
            ],
            'weak_crypto' => [
                '/md5\s*\(/i',
                '/sha1\s*\(/i',
                '/base64_encode\s*\(/i'
            ]
        ];
    }

    private function scanPhpSecurity(string $projectPath, array $options): array
    {
        $results = [
            'php_vulnerabilities' => [],
            'files_scanned' => 0
        ];

        $phpExtensions = $this->config->getFileExtensions()['php'] ?? ['php'];
        $files = $this->getFilesToScan($projectPath, $phpExtensions, $options['exclude_dirs'] ?? []);

        foreach ($files as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $this->getRelativePath($filePath, $projectPath);
            $content = file_get_contents($filePath);
            
            $results['files_scanned']++;
            
            $vulnerabilities = $this->scanFileForVulnerabilities($content, $relativePath, 'php');
            $results['php_vulnerabilities'] = array_merge($results['php_vulnerabilities'], $vulnerabilities);
        }

        return $results;
    }

    private function scanJavaScriptSecurity(string $projectPath, array $options): array
    {
        $results = [
            'js_vulnerabilities' => [],
            'files_scanned' => 0
        ];

        $jsExtensions = array_merge(
            $this->config->getFileExtensions()['javascript'] ?? [],
            $this->config->getFileExtensions()['vue'] ?? []
        );

        if (empty($jsExtensions)) {
            return $results;
        }

        $files = $this->getFilesToScan($projectPath, $jsExtensions, $options['exclude_dirs'] ?? []);

        foreach ($files as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $this->getRelativePath($filePath, $projectPath);
            $content = file_get_contents($filePath);
            
            $results['files_scanned']++;
            
            $vulnerabilities = $this->scanJavaScriptFileForVulnerabilities($content, $relativePath);
            $results['js_vulnerabilities'] = array_merge($results['js_vulnerabilities'], $vulnerabilities);
        }

        return $results;
    }

    private function scanConfigurationSecurity(string $projectPath, array $options): array
    {
        $results = [
            'config_vulnerabilities' => []
        ];

        // Check .env files
        $envFiles = ['.env', '.env.example', '.env.local', '.env.production'];
        foreach ($envFiles as $envFile) {
            $envPath = $projectPath . '/' . $envFile;
            if (file_exists($envPath)) {
                $content = file_get_contents($envPath);
                $vulnerabilities = $this->scanEnvFile($content, $envFile);
                $results['config_vulnerabilities'] = array_merge($results['config_vulnerabilities'], $vulnerabilities);
            }
        }

        // Check Laravel config files
        $configDir = $projectPath . '/config';
        if (is_dir($configDir)) {
            $configFiles = glob($configDir . '/*.php');
            foreach ($configFiles as $configFile) {
                $relativePath = $this->getRelativePath($configFile, $projectPath);
                $content = file_get_contents($configFile);
                $vulnerabilities = $this->scanConfigFile($content, $relativePath);
                $results['config_vulnerabilities'] = array_merge($results['config_vulnerabilities'], $vulnerabilities);
            }
        }

        return $results;
    }

    private function scanFileForVulnerabilities(string $content, string $filePath, string $language): array
    {
        $vulnerabilities = [];
        $lines = explode("\n", $content);

        foreach ($this->securityPatterns as $vulnerabilityType => $patterns) {
            if (!$this->shouldCheckVulnerability($vulnerabilityType)) {
                continue;
            }

            foreach ($patterns as $pattern) {
                foreach ($lines as $lineNumber => $line) {
                    if (preg_match($pattern, $line, $matches)) {
                        $vulnerabilities[] = [
                            'type' => $vulnerabilityType,
                            'severity' => $this->getVulnerabilitySeverity($vulnerabilityType),
                            'file' => $filePath,
                            'line' => $lineNumber + 1,
                            'message' => $this->getVulnerabilityMessage($vulnerabilityType),
                            'code_snippet' => trim($line),
                            'language' => $language
                        ];
                    }
                }
            }
        }

        return $vulnerabilities;
    }

    private function scanJavaScriptFileForVulnerabilities(string $content, string $filePath): array
    {
        $vulnerabilities = [];
        $lines = explode("\n", $content);

        $jsPatterns = [
            'xss' => [
                '/innerHTML\s*=\s*[^;]*(?:location\.search|document\.URL|window\.location)/i',
                '/document\.write\s*\([^)]*(?:location\.search|document\.URL)/i',
                '/eval\s*\([^)]*(?:location\.search|document\.URL)/i'
            ],
            'insecure_random' => [
                '/Math\.random\s*\(\s*\)/i'
            ],
            'hardcoded_secrets' => [
                '/(?:api_key|secret|token|password)\s*[:=]\s*[\'"][^\'"]{8,}[\'"]/i'
            ]
        ];

        foreach ($jsPatterns as $vulnerabilityType => $patterns) {
            foreach ($patterns as $pattern) {
                foreach ($lines as $lineNumber => $line) {
                    if (preg_match($pattern, $line, $matches)) {
                        $vulnerabilities[] = [
                            'type' => $vulnerabilityType,
                            'severity' => $this->getVulnerabilitySeverity($vulnerabilityType),
                            'file' => $filePath,
                            'line' => $lineNumber + 1,
                            'message' => $this->getVulnerabilityMessage($vulnerabilityType),
                            'code_snippet' => trim($line),
                            'language' => 'javascript'
                        ];
                    }
                }
            }
        }

        return $vulnerabilities;
    }

    private function scanEnvFile(string $content, string $filePath): array
    {
        $vulnerabilities = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $line = trim($line);
            
            // Check for hardcoded secrets in production env
            if (str_contains($filePath, '.env') && !str_contains($filePath, 'example')) {
                if (preg_match('/^(?:DB_PASSWORD|API_KEY|SECRET_KEY|JWT_SECRET)=.+/i', $line)) {
                    $vulnerabilities[] = [
                        'type' => 'exposed_secrets',
                        'severity' => 'high',
                        'file' => $filePath,
                        'line' => $lineNumber + 1,
                        'message' => 'Sensitive configuration exposed in environment file',
                        'code_snippet' => $line
                    ];
                }
            }

            // Check for debug mode in production
            if (preg_match('/^APP_DEBUG=true/i', $line)) {
                $vulnerabilities[] = [
                    'type' => 'debug_mode',
                    'severity' => 'medium',
                    'file' => $filePath,
                    'line' => $lineNumber + 1,
                    'message' => 'Debug mode enabled - should be disabled in production',
                    'code_snippet' => $line
                ];
            }
        }

        return $vulnerabilities;
    }

    private function scanConfigFile(string $content, string $filePath): array
    {
        $vulnerabilities = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            // Check for hardcoded credentials
            if (preg_match('/[\'"](?:password|secret|key)[\'"].*=>\s*[\'"][^\'"]{3,}[\'"]/i', $line)) {
                $vulnerabilities[] = [
                    'type' => 'hardcoded_credentials',
                    'severity' => 'high',
                    'file' => $filePath,
                    'line' => $lineNumber + 1,
                    'message' => 'Hardcoded credentials found in configuration file',
                    'code_snippet' => trim($line)
                ];
            }
        }

        return $vulnerabilities;
    }

    private function shouldCheckVulnerability(string $type): bool
    {
        $rules = $this->config->getSecurityRules();
        
        $checkMap = [
            'sql_injection' => 'check_sql_injection',
            'xss' => 'check_xss',
            'file_inclusion' => 'check_file_upload',
            'command_injection' => 'check_input_validation',
            'file_upload' => 'check_file_upload'
        ];

        $ruleKey = $checkMap[$type] ?? 'check_' . $type;
        return $rules[$ruleKey] ?? true;
    }

    private function getVulnerabilitySeverity(string $type): string
    {
        $severityMap = [
            'sql_injection' => 'critical',
            'xss' => 'high',
            'command_injection' => 'critical',
            'file_inclusion' => 'high',
            'file_upload' => 'medium',
            'hardcoded_secrets' => 'high',
            'weak_crypto' => 'medium',
            'insecure_random' => 'low'
        ];

        return $severityMap[$type] ?? 'medium';
    }

    private function getVulnerabilityMessage(string $type): string
    {
        $messages = [
            'sql_injection' => 'Potential SQL injection vulnerability detected',
            'xss' => 'Potential Cross-Site Scripting (XSS) vulnerability detected',
            'command_injection' => 'Potential command injection vulnerability detected',
            'file_inclusion' => 'Potential file inclusion vulnerability detected',
            'file_upload' => 'Insecure file upload handling detected',
            'hardcoded_secrets' => 'Hardcoded secrets or credentials detected',
            'weak_crypto' => 'Weak cryptographic function usage detected',
            'insecure_random' => 'Insecure random number generation detected'
        ];

        return $messages[$type] ?? 'Security vulnerability detected';
    }

    private function calculateSecurityScore(array $results): float
    {
        $totalVulnerabilities = 0;
        $criticalCount = 0;
        $highCount = 0;
        $mediumCount = 0;
        $lowCount = 0;

        // Count vulnerabilities by severity
        foreach (['php_vulnerabilities', 'js_vulnerabilities', 'config_vulnerabilities'] as $category) {
            foreach ($results[$category] ?? [] as $vuln) {
                $totalVulnerabilities++;
                switch ($vuln['severity']) {
                    case 'critical':
                        $criticalCount++;
                        break;
                    case 'high':
                        $highCount++;
                        break;
                    case 'medium':
                        $mediumCount++;
                        break;
                    case 'low':
                        $lowCount++;
                        break;
                }
            }
        }

        if ($totalVulnerabilities === 0) {
            return 100;
        }

        // Calculate weighted score
        $score = 100 - ($criticalCount * 25) - ($highCount * 15) - ($mediumCount * 8) - ($lowCount * 3);
        
        return max(0, $score);
    }
}
