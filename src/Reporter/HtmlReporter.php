<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ea<PERSON>\LaravelChecker\Reporter;

use ZTeam\LaravelChecker\Config\Configuration;

/**
 * HTML reporter
 * 
 * Generates HTML format reports with interactive features.
 */
class HtmlReporter implements ReporterInterface
{
    private Configuration $config;

    public function __construct(Configuration $config)
    {
        $this->config = $config;
    }

    public function generate(array $results, string $outputDir): void
    {
        $htmlContent = $this->generateHtmlReport($results);
        
        $outputFile = $outputDir . '/laravel-checker-report.html';
        file_put_contents($outputFile, $htmlContent);
        
        // Copy CSS and JS assets
        $this->copyAssets($outputDir);
    }

    private function generateHtmlReport(array $results): string
    {
        $summary = $results['summary'] ?? [];
        
        $html = $this->getHtmlTemplate();
        
        // Replace placeholders
        $replacements = [
            '{{TITLE}}' => 'Laravel Checker Report',
            '{{SCAN_TIME}}' => $results['scan_time'] ?? date('Y-m-d H:i:s'),
            '{{PROJECT_PATH}}' => $results['project_path'] ?? 'Unknown',
            '{{OVERALL_SCORE}}' => number_format($summary['overall_score'] ?? 0, 1),
            '{{QUALITY_SCORE}}' => number_format($summary['quality_score'] ?? 0, 1),
            '{{SECURITY_SCORE}}' => number_format($summary['security_score'] ?? 0, 1),
            '{{COVERAGE_SCORE}}' => number_format($summary['coverage_score'] ?? 0, 1),
            '{{DEPENDENCY_SCORE}}' => number_format($summary['dependency_score'] ?? 0, 1),
            '{{TOTAL_FILES}}' => number_format($summary['total_files'] ?? 0),
            '{{TOTAL_LINES}}' => number_format($summary['total_lines'] ?? 0),
            '{{CODE_ISSUES}}' => number_format($summary['code_issues'] ?? 0),
            '{{SECURITY_ISSUES}}' => number_format($summary['security_issues'] ?? 0),
            '{{OUTDATED_DEPS}}' => number_format($summary['outdated_deps'] ?? 0),
            '{{TEST_COVERAGE}}' => number_format($summary['test_coverage'] ?? 0, 1),
            '{{RELEASE_READINESS}}' => $this->getReadinessHtml($summary['release_readiness'] ?? 'unknown'),
            '{{QUALITY_DETAILS}}' => $this->generateQualitySection($results['quality'] ?? []),
            '{{SECURITY_DETAILS}}' => $this->generateSecuritySection($results['security'] ?? []),
            '{{DEPENDENCY_DETAILS}}' => $this->generateDependencySection($results['dependencies'] ?? []),
            '{{COVERAGE_DETAILS}}' => $this->generateCoverageSection($results['coverage'] ?? []),
            '{{RECOMMENDATIONS}}' => $this->generateRecommendationsSection($summary['recommendations'] ?? []),
            '{{CHARTS_DATA}}' => $this->generateChartsData($results)
        ];
        
        foreach ($replacements as $placeholder => $value) {
            $html = str_replace($placeholder, $value, $html);
        }
        
        return $html;
    }

    private function getHtmlTemplate(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .score-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .score-excellent { background: linear-gradient(135deg, #28a745, #20c997); color: white; }
        .score-good { background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white; }
        .score-fair { background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; }
        .score-poor { background: linear-gradient(135deg, #dc3545, #e83e8c); color: white; }
        .issue-critical { border-left: 4px solid #dc3545; }
        .issue-high { border-left: 4px solid #fd7e14; }
        .issue-medium { border-left: 4px solid #ffc107; }
        .issue-low { border-left: 4px solid #28a745; }
        .readiness-ready { color: #28a745; }
        .readiness-warning { color: #ffc107; }
        .readiness-not-ready { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4"><i class="fas fa-search"></i> {{TITLE}}</h1>
                <div class="text-center text-muted mb-4">
                    Generated: {{SCAN_TIME}} | Project: {{PROJECT_PATH}}
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card score-card {{OVERALL_SCORE_CLASS}}">
                    <div class="card-body text-center">
                        <h3>{{OVERALL_SCORE}}</h3>
                        <p class="mb-0">Overall Score</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card score-card {{QUALITY_SCORE_CLASS}}">
                    <div class="card-body text-center">
                        <h3>{{QUALITY_SCORE}}</h3>
                        <p class="mb-0">Quality</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card score-card {{SECURITY_SCORE_CLASS}}">
                    <div class="card-body text-center">
                        <h3>{{SECURITY_SCORE}}</h3>
                        <p class="mb-0">Security</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card score-card {{COVERAGE_SCORE_CLASS}}">
                    <div class="card-body text-center">
                        <h3>{{COVERAGE_SCORE}}</h3>
                        <p class="mb-0">Coverage</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card score-card {{DEPENDENCY_SCORE_CLASS}}">
                    <div class="card-body text-center">
                        <h3>{{DEPENDENCY_SCORE}}</h3>
                        <p class="mb-0">Dependencies</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card">
                    <div class="card-body text-center">
                        {{RELEASE_READINESS}}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>{{TOTAL_FILES}}</h4>
                        <p class="text-muted mb-0">Files Scanned</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>{{TOTAL_LINES}}</h4>
                        <p class="text-muted mb-0">Lines of Code</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>{{CODE_ISSUES}}</h4>
                        <p class="text-muted mb-0">Code Issues</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>{{SECURITY_ISSUES}}</h4>
                        <p class="text-muted mb-0">Security Issues</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Score Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="scoreChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Issues by Category</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="issuesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Sections -->
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-code"></i> Code Quality</h5>
                    </div>
                    <div class="card-body">
                        {{QUALITY_DETAILS}}
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> Security</h5>
                    </div>
                    <div class="card-body">
                        {{SECURITY_DETAILS}}
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cubes"></i> Dependencies</h5>
                    </div>
                    <div class="card-body">
                        {{DEPENDENCY_DETAILS}}
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Test Coverage</h5>
                    </div>
                    <div class="card-body">
                        {{COVERAGE_DETAILS}}
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb"></i> Recommendations</h5>
                    </div>
                    <div class="card-body">
                        {{RECOMMENDATIONS}}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        {{CHARTS_DATA}}
    </script>
</body>
</html>';
    }

    private function getScoreClass(float $score): string
    {
        if ($score >= 80) return 'score-excellent';
        if ($score >= 60) return 'score-good';
        if ($score >= 40) return 'score-fair';
        return 'score-poor';
    }

    private function getReadinessHtml(string $readiness): string
    {
        switch ($readiness) {
            case 'ready':
                return '<i class="fas fa-check-circle readiness-ready"></i><br><small>Ready</small>';
            case 'ready_with_warnings':
                return '<i class="fas fa-exclamation-triangle readiness-warning"></i><br><small>Ready with Warnings</small>';
            case 'not_ready':
                return '<i class="fas fa-times-circle readiness-not-ready"></i><br><small>Not Ready</small>';
            default:
                return '<i class="fas fa-question-circle text-muted"></i><br><small>Unknown</small>';
        }
    }

    private function generateQualitySection(array $quality): string
    {
        if (empty($quality)) {
            return '<p class="text-muted">No quality analysis performed.</p>';
        }

        $html = '<div class="row">';
        
        // PHP Quality
        if (!empty($quality['php'])) {
            $php = $quality['php'];
            $html .= '<div class="col-md-6">';
            $html .= '<h6>PHP Analysis</h6>';
            $html .= '<ul>';
            $html .= '<li>Files: ' . ($php['files_scanned'] ?? 0) . '</li>';
            $html .= '<li>Lines of Code: ' . number_format($php['lines_of_code'] ?? 0) . '</li>';
            $html .= '<li>Issues: ' . count($php['issues'] ?? []) . '</li>';
            $html .= '</ul>';
            $html .= '</div>';
        }

        // JavaScript Quality
        if (!empty($quality['javascript'])) {
            $js = $quality['javascript'];
            $html .= '<div class="col-md-6">';
            $html .= '<h6>JavaScript Analysis</h6>';
            $html .= '<ul>';
            $html .= '<li>Files: ' . ($js['files_scanned'] ?? 0) . '</li>';
            $html .= '<li>Lines of Code: ' . number_format($js['lines_of_code'] ?? 0) . '</li>';
            $html .= '<li>Issues: ' . count($js['issues'] ?? []) . '</li>';
            $html .= '</ul>';
            $html .= '</div>';
        }

        $html .= '</div>';

        // Tools used
        if (!empty($quality['tools_used'])) {
            $html .= '<p><strong>Tools used:</strong> ' . implode(', ', $quality['tools_used']) . '</p>';
        }

        return $html;
    }

    private function generateSecuritySection(array $security): string
    {
        if (empty($security)) {
            return '<p class="text-muted">No security analysis performed.</p>';
        }

        $totalVulns = 0;
        $vulnsBySeverity = ['critical' => 0, 'high' => 0, 'medium' => 0, 'low' => 0];

        foreach (['php_vulnerabilities', 'js_vulnerabilities', 'config_vulnerabilities'] as $category) {
            foreach ($security[$category] ?? [] as $vuln) {
                $totalVulns++;
                $severity = $vuln['severity'] ?? 'medium';
                if (isset($vulnsBySeverity[$severity])) {
                    $vulnsBySeverity[$severity]++;
                }
            }
        }

        $html = '<div class="row">';
        $html .= '<div class="col-md-6">';
        $html .= '<h6>Vulnerability Summary</h6>';
        $html .= '<ul>';
        $html .= '<li>Total Vulnerabilities: ' . $totalVulns . '</li>';
        foreach ($vulnsBySeverity as $severity => $count) {
            if ($count > 0) {
                $html .= '<li>' . ucfirst($severity) . ': ' . $count . '</li>';
            }
        }
        $html .= '</ul>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    private function generateDependencySection(array $dependencies): string
    {
        if (empty($dependencies)) {
            return '<p class="text-muted">No dependency analysis performed.</p>';
        }

        $html = '<div class="row">';
        $html .= '<div class="col-md-6">';
        $html .= '<h6>Dependency Summary</h6>';
        $html .= '<ul>';
        $html .= '<li>Total Dependencies: ' . ($dependencies['total_dependencies'] ?? 0) . '</li>';
        $html .= '<li>Outdated: ' . count($dependencies['outdated_packages'] ?? []) . '</li>';
        $html .= '<li>Vulnerable: ' . count($dependencies['vulnerable_packages'] ?? []) . '</li>';
        $html .= '</ul>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    private function generateCoverageSection(array $coverage): string
    {
        if (empty($coverage)) {
            return '<p class="text-muted">No coverage analysis performed.</p>';
        }

        $html = '<div class="row">';
        $html .= '<div class="col-md-6">';
        $html .= '<h6>Coverage Summary</h6>';
        $html .= '<ul>';
        $html .= '<li>Overall Coverage: ' . number_format($coverage['coverage_percentage'] ?? 0, 1) . '%</li>';
        $html .= '<li>Files Covered: ' . ($coverage['files_covered'] ?? 0) . '</li>';
        $html .= '<li>Total Files: ' . ($coverage['total_files'] ?? 0) . '</li>';
        $html .= '</ul>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    private function generateRecommendationsSection(array $recommendations): string
    {
        if (empty($recommendations)) {
            return '<p class="text-success">No recommendations - everything looks good!</p>';
        }

        $html = '';
        foreach ($recommendations as $rec) {
            $priority = $rec['priority'] ?? 'medium';
            $category = $rec['category'] ?? 'general';
            $message = $rec['message'] ?? '';
            $action = $rec['action'] ?? '';

            $alertClass = $this->getPriorityAlertClass($priority);
            
            $html .= '<div class="alert ' . $alertClass . '">';
            $html .= '<h6><i class="fas fa-exclamation-triangle"></i> ' . ucfirst($category) . ' (' . ucfirst($priority) . ' Priority)</h6>';
            $html .= '<p>' . htmlspecialchars($message) . '</p>';
            if ($action) {
                $html .= '<small><strong>Action:</strong> ' . htmlspecialchars($action) . '</small>';
            }
            $html .= '</div>';
        }

        return $html;
    }

    private function getPriorityAlertClass(string $priority): string
    {
        switch ($priority) {
            case 'critical':
                return 'alert-danger';
            case 'high':
                return 'alert-warning';
            case 'medium':
                return 'alert-info';
            case 'low':
                return 'alert-light';
            default:
                return 'alert-secondary';
        }
    }

    private function generateChartsData(array $results): string
    {
        $summary = $results['summary'] ?? [];
        
        return '
        // Score Chart
        const scoreCtx = document.getElementById("scoreChart").getContext("2d");
        new Chart(scoreCtx, {
            type: "radar",
            data: {
                labels: ["Quality", "Security", "Coverage", "Dependencies"],
                datasets: [{
                    label: "Scores",
                    data: [' . ($summary['quality_score'] ?? 0) . ', ' . ($summary['security_score'] ?? 0) . ', ' . ($summary['coverage_score'] ?? 0) . ', ' . ($summary['dependency_score'] ?? 0) . '],
                    backgroundColor: "rgba(54, 162, 235, 0.2)",
                    borderColor: "rgba(54, 162, 235, 1)",
                    borderWidth: 2
                }]
            },
            options: {
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Issues Chart
        const issuesCtx = document.getElementById("issuesChart").getContext("2d");
        new Chart(issuesCtx, {
            type: "doughnut",
            data: {
                labels: ["Code Issues", "Security Issues", "Dependency Issues"],
                datasets: [{
                    data: [' . ($summary['code_issues'] ?? 0) . ', ' . ($summary['security_issues'] ?? 0) . ', ' . ($summary['outdated_deps'] ?? 0) . '],
                    backgroundColor: ["#36A2EB", "#FF6384", "#FFCE56"]
                }]
            }
        });
        ';
    }

    private function copyAssets(string $outputDir): void
    {
        // In a real implementation, you might want to copy CSS/JS assets
        // For now, we're using CDN links in the template
    }
}
