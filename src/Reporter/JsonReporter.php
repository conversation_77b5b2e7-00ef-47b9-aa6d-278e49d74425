<?php

declare(strict_types=1);

namespace ZTeam\LaravelChecker\Reporter;

/**
 * JSON reporter
 * 
 * Generates JSON format reports.
 */
class JsonReporter implements ReporterInterface
{
    public function generate(array $results, string $outputDir): void
    {
        $jsonData = $this->formatResults($results);
        
        $outputFile = $outputDir . '/laravel-checker-report.json';
        file_put_contents($outputFile, json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        
        // Also generate a summary JSON
        $summaryFile = $outputDir . '/summary.json';
        file_put_contents($summaryFile, json_encode($results['summary'] ?? [], JSON_PRETTY_PRINT));
    }

    private function formatResults(array $results): array
    {
        return [
            'meta' => [
                'tool' => 'Laravel Checker',
                'version' => '1.0.0',
                'scan_time' => $results['scan_time'] ?? date('Y-m-d H:i:s'),
                'project_path' => $results['project_path'] ?? '',
                'options' => $results['options'] ?? []
            ],
            'summary' => $results['summary'] ?? [],
            'quality' => $this->formatQualityResults($results['quality'] ?? []),
            'security' => $this->formatSecurityResults($results['security'] ?? []),
            'dependencies' => $this->formatDependencyResults($results['dependencies'] ?? []),
            'coverage' => $this->formatCoverageResults($results['coverage'] ?? [])
        ];
    }

    private function formatQualityResults(array $quality): array
    {
        return [
            'score' => $quality['metrics']['quality_score'] ?? 0,
            'total_files' => ($quality['php']['files_scanned'] ?? 0) + ($quality['javascript']['files_scanned'] ?? 0),
            'total_lines' => ($quality['php']['lines_of_code'] ?? 0) + ($quality['javascript']['lines_of_code'] ?? 0),
            'issues' => [
                'total' => $this->countTotalIssues($quality),
                'by_severity' => $this->groupIssuesBySeverity($quality),
                'by_type' => $this->groupIssuesByType($quality),
                'details' => $this->getAllIssues($quality)
            ],
            'tools_used' => $quality['tools_used'] ?? []
        ];
    }

    private function formatSecurityResults(array $security): array
    {
        $allVulnerabilities = [];
        
        foreach (['php_vulnerabilities', 'js_vulnerabilities', 'config_vulnerabilities'] as $category) {
            $allVulnerabilities = array_merge($allVulnerabilities, $security[$category] ?? []);
        }

        return [
            'score' => $security['security_score'] ?? 0,
            'total_vulnerabilities' => count($allVulnerabilities),
            'vulnerabilities' => [
                'by_severity' => $this->groupVulnerabilitiesBySeverity($allVulnerabilities),
                'by_type' => $this->groupVulnerabilitiesByType($allVulnerabilities),
                'details' => $allVulnerabilities
            ],
            'files_scanned' => $security['files_scanned'] ?? 0
        ];
    }

    private function formatDependencyResults(array $dependencies): array
    {
        return [
            'total_dependencies' => $dependencies['total_dependencies'] ?? 0,
            'outdated' => [
                'count' => count($dependencies['outdated_packages'] ?? []),
                'packages' => $dependencies['outdated_packages'] ?? []
            ],
            'vulnerable' => [
                'count' => count($dependencies['vulnerable_packages'] ?? []),
                'packages' => $dependencies['vulnerable_packages'] ?? []
            ],
            'composer' => $dependencies['composer'] ?? [],
            'npm' => $dependencies['npm'] ?? [],
            'tools_used' => $dependencies['tools_used'] ?? []
        ];
    }

    private function formatCoverageResults(array $coverage): array
    {
        return [
            'overall_coverage' => $coverage['coverage_percentage'] ?? 0,
            'line_coverage' => $coverage['line_coverage'] ?? 0,
            'branch_coverage' => $coverage['branch_coverage'] ?? 0,
            'function_coverage' => $coverage['function_coverage'] ?? 0,
            'files_covered' => $coverage['files_covered'] ?? 0,
            'total_files' => $coverage['total_files'] ?? 0,
            'uncovered_files' => $coverage['uncovered_files'] ?? [],
            'php_coverage' => $coverage['php_coverage'] ?? [],
            'js_coverage' => $coverage['js_coverage'] ?? [],
            'tools_used' => $coverage['tools_used'] ?? []
        ];
    }

    private function countTotalIssues(array $quality): int
    {
        $total = 0;
        foreach (['php', 'javascript'] as $lang) {
            $total += count($quality[$lang]['issues'] ?? []);
        }
        return $total;
    }

    private function groupIssuesBySeverity(array $quality): array
    {
        $severityGroups = ['error' => 0, 'warning' => 0, 'info' => 0];
        
        foreach (['php', 'javascript'] as $lang) {
            foreach ($quality[$lang]['issues'] ?? [] as $issue) {
                $severity = $issue['severity'] ?? 'info';
                if (isset($severityGroups[$severity])) {
                    $severityGroups[$severity]++;
                }
            }
        }
        
        return $severityGroups;
    }

    private function groupIssuesByType(array $quality): array
    {
        $typeGroups = [];
        
        foreach (['php', 'javascript'] as $lang) {
            foreach ($quality[$lang]['issues'] ?? [] as $issue) {
                $type = $issue['type'] ?? 'unknown';
                $typeGroups[$type] = ($typeGroups[$type] ?? 0) + 1;
            }
        }
        
        return $typeGroups;
    }

    private function getAllIssues(array $quality): array
    {
        $allIssues = [];
        
        foreach (['php', 'javascript'] as $lang) {
            $allIssues = array_merge($allIssues, $quality[$lang]['issues'] ?? []);
        }
        
        return $allIssues;
    }

    private function groupVulnerabilitiesBySeverity(array $vulnerabilities): array
    {
        $severityGroups = ['critical' => 0, 'high' => 0, 'medium' => 0, 'low' => 0];
        
        foreach ($vulnerabilities as $vuln) {
            $severity = $vuln['severity'] ?? 'medium';
            if (isset($severityGroups[$severity])) {
                $severityGroups[$severity]++;
            }
        }
        
        return $severityGroups;
    }

    private function groupVulnerabilitiesByType(array $vulnerabilities): array
    {
        $typeGroups = [];
        
        foreach ($vulnerabilities as $vuln) {
            $type = $vuln['type'] ?? 'unknown';
            $typeGroups[$type] = ($typeGroups[$type] ?? 0) + 1;
        }
        
        return $typeGroups;
    }
}
