<?php

declare(strict_types=1);

namespace ZTeam\LaravelChecker\Reporter;

/**
 * Console reporter
 * 
 * Generates console-friendly text reports.
 */
class ConsoleReporter implements ReporterInterface
{
    public function generate(array $results, string $outputDir): void
    {
        $report = $this->generateTextReport($results);
        
        $outputFile = $outputDir . '/console-report.txt';
        file_put_contents($outputFile, $report);
    }

    private function generateTextReport(array $results): string
    {
        $report = [];
        $report[] = "=== LARAVEL CHECKER REPORT ===";
        $report[] = "Generated: " . ($results['scan_time'] ?? date('Y-m-d H:i:s'));
        $report[] = "Project: " . ($results['project_path'] ?? 'Unknown');
        $report[] = "";

        // Summary section
        $summary = $results['summary'] ?? [];
        $report[] = "=== SUMMARY ===";
        $report[] = sprintf("Overall Score: %.1f/100", $summary['overall_score'] ?? 0);
        $report[] = sprintf("Quality Score: %.1f/100", $summary['quality_score'] ?? 0);
        $report[] = sprintf("Security Score: %.1f/100", $summary['security_score'] ?? 0);
        $report[] = sprintf("Coverage Score: %.1f/100", $summary['coverage_score'] ?? 0);
        $report[] = sprintf("Dependency Score: %.1f/100", $summary['dependency_score'] ?? 0);
        $report[] = "";
        $report[] = sprintf("Total Files: %d", $summary['total_files'] ?? 0);
        $report[] = sprintf("Total Lines: %d", $summary['total_lines'] ?? 0);
        $report[] = sprintf("Code Issues: %d", $summary['code_issues'] ?? 0);
        $report[] = sprintf("Security Issues: %d", $summary['security_issues'] ?? 0);
        $report[] = sprintf("Outdated Dependencies: %d", $summary['outdated_deps'] ?? 0);
        $report[] = sprintf("Test Coverage: %.1f%%", $summary['test_coverage'] ?? 0);
        $report[] = "";

        // Release readiness
        $readiness = $summary['release_readiness'] ?? 'unknown';
        $readinessText = $this->getReadinessText($readiness);
        $report[] = "Release Readiness: " . $readinessText;
        $report[] = "";

        // Quality section
        if (!empty($results['quality'])) {
            $report[] = "=== CODE QUALITY ===";
            $quality = $results['quality'];
            
            if (!empty($quality['php'])) {
                $report[] = sprintf("PHP Files: %d (%.1fK LOC)", 
                    $quality['php']['files_scanned'] ?? 0,
                    ($quality['php']['lines_of_code'] ?? 0) / 1000
                );
                $report[] = sprintf("PHP Issues: %d", count($quality['php']['issues'] ?? []));
            }
            
            if (!empty($quality['javascript'])) {
                $report[] = sprintf("JS Files: %d (%.1fK LOC)", 
                    $quality['javascript']['files_scanned'] ?? 0,
                    ($quality['javascript']['lines_of_code'] ?? 0) / 1000
                );
                $report[] = sprintf("JS Issues: %d", count($quality['javascript']['issues'] ?? []));
            }
            
            $report[] = sprintf("Tools Used: %s", implode(', ', $quality['tools_used'] ?? []));
            $report[] = "";
        }

        // Security section
        if (!empty($results['security'])) {
            $report[] = "=== SECURITY ===";
            $security = $results['security'];
            
            $totalVulns = 0;
            $vulnsBySeverity = ['critical' => 0, 'high' => 0, 'medium' => 0, 'low' => 0];
            
            foreach (['php_vulnerabilities', 'js_vulnerabilities', 'config_vulnerabilities'] as $category) {
                foreach ($security[$category] ?? [] as $vuln) {
                    $totalVulns++;
                    $severity = $vuln['severity'] ?? 'medium';
                    if (isset($vulnsBySeverity[$severity])) {
                        $vulnsBySeverity[$severity]++;
                    }
                }
            }
            
            $report[] = sprintf("Total Vulnerabilities: %d", $totalVulns);
            foreach ($vulnsBySeverity as $severity => $count) {
                if ($count > 0) {
                    $report[] = sprintf("  %s: %d", ucfirst($severity), $count);
                }
            }
            $report[] = "";
        }

        // Dependencies section
        if (!empty($results['dependencies'])) {
            $report[] = "=== DEPENDENCIES ===";
            $deps = $results['dependencies'];
            
            $report[] = sprintf("Total Dependencies: %d", $deps['total_dependencies'] ?? 0);
            $report[] = sprintf("Outdated: %d", count($deps['outdated_packages'] ?? []));
            $report[] = sprintf("Vulnerable: %d", count($deps['vulnerable_packages'] ?? []));
            
            if (!empty($deps['composer']['dependencies'])) {
                $report[] = sprintf("Composer Packages: %d", count($deps['composer']['dependencies']));
            }
            
            if (!empty($deps['npm']['dependencies'])) {
                $report[] = sprintf("NPM Packages: %d", count($deps['npm']['dependencies']));
            }
            
            $report[] = "";
        }

        // Coverage section
        if (!empty($results['coverage'])) {
            $report[] = "=== TEST COVERAGE ===";
            $coverage = $results['coverage'];
            
            $report[] = sprintf("Overall Coverage: %.1f%%", $coverage['coverage_percentage'] ?? 0);
            
            if (!empty($coverage['php_coverage'])) {
                $phpCov = $coverage['php_coverage'];
                $report[] = sprintf("PHP Line Coverage: %.1f%%", $phpCov['line_coverage'] ?? 0);
                $report[] = sprintf("PHP Method Coverage: %.1f%%", $phpCov['method_coverage'] ?? 0);
            }
            
            if (!empty($coverage['js_coverage'])) {
                $jsCov = $coverage['js_coverage'];
                $report[] = sprintf("JS Line Coverage: %.1f%%", $jsCov['line_coverage'] ?? 0);
                $report[] = sprintf("JS Branch Coverage: %.1f%%", $jsCov['branch_coverage'] ?? 0);
            }
            
            $report[] = "";
        }

        // Recommendations section
        if (!empty($summary['recommendations'])) {
            $report[] = "=== RECOMMENDATIONS ===";
            
            foreach ($summary['recommendations'] as $rec) {
                $priority = strtoupper($rec['priority'] ?? 'MEDIUM');
                $category = strtoupper($rec['category'] ?? 'GENERAL');
                $report[] = sprintf("[%s] %s: %s", $priority, $category, $rec['message'] ?? '');
                $report[] = sprintf("  Action: %s", $rec['action'] ?? '');
                $report[] = "";
            }
        }

        // Top issues section
        $report = array_merge($report, $this->generateTopIssuesSection($results));

        return implode("\n", $report);
    }

    private function getReadinessText(string $readiness): string
    {
        switch ($readiness) {
            case 'ready':
                return '✅ READY FOR RELEASE';
            case 'ready_with_warnings':
                return '⚠️  READY WITH WARNINGS';
            case 'not_ready':
                return '❌ NOT READY FOR RELEASE';
            default:
                return '❓ UNKNOWN';
        }
    }

    private function generateTopIssuesSection(array $results): array
    {
        $section = [];
        $section[] = "=== TOP ISSUES ===";

        // Collect all issues
        $allIssues = [];
        
        // Quality issues
        foreach (['php', 'javascript'] as $lang) {
            foreach ($results['quality'][$lang]['issues'] ?? [] as $issue) {
                $allIssues[] = array_merge($issue, ['category' => 'quality']);
            }
        }
        
        // Security issues
        foreach (['php_vulnerabilities', 'js_vulnerabilities', 'config_vulnerabilities'] as $category) {
            foreach ($results['security'][$category] ?? [] as $issue) {
                $allIssues[] = array_merge($issue, ['category' => 'security']);
            }
        }

        // Sort by severity
        usort($allIssues, function($a, $b) {
            $severityOrder = ['critical' => 4, 'error' => 3, 'high' => 3, 'warning' => 2, 'medium' => 2, 'info' => 1, 'low' => 1];
            $aSeverity = $severityOrder[$a['severity'] ?? 'info'] ?? 1;
            $bSeverity = $severityOrder[$b['severity'] ?? 'info'] ?? 1;
            return $bSeverity - $aSeverity;
        });

        // Show top 10 issues
        $topIssues = array_slice($allIssues, 0, 10);
        
        foreach ($topIssues as $issue) {
            $severity = strtoupper($issue['severity'] ?? 'INFO');
            $category = strtoupper($issue['category'] ?? 'GENERAL');
            $file = $issue['file'] ?? 'Unknown';
            $line = $issue['line'] ?? '?';
            $message = $issue['message'] ?? 'No description';
            
            $section[] = sprintf("[%s] %s in %s:%s", $severity, $category, $file, $line);
            $section[] = sprintf("  %s", $message);
            $section[] = "";
        }

        if (empty($topIssues)) {
            $section[] = "No issues found!";
            $section[] = "";
        }

        return $section;
    }
}
