<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ea<PERSON>\LaravelChecker;

use ZTeam\LaravelChecker\Config\Configuration;
use ZTeam\LaravelChecker\Scanner\QualityScanner;
use Z<PERSON>eam\LaravelChecker\Scanner\SecurityScanner;
use <PERSON><PERSON>eam\LaravelChecker\Scanner\DependencyScanner;
use ZTeam\LaravelChecker\Scanner\CoverageScanner;
use ZTeam\LaravelChecker\Analyzer\ResultAnalyzer;
use ZTeam\LaravelChecker\Reporter\JsonReporter;
use ZTeam\LaravelChecker\Reporter\HtmlReporter;
use ZTeam\LaravelChecker\Reporter\ConsoleReporter;

/**
 * Main Laravel Checker class
 * 
 * Orchestrates the scanning process and coordinates all scanners and reporters.
 */
class LaravelChecker
{
    private Configuration $config;
    private QualityScanner $qualityScanner;
    private SecurityScanner $securityScanner;
    private DependencyScanner $dependencyScanner;
    private CoverageScanner $coverageScanner;
    private ResultAnalyzer $analyzer;

    public function __construct(Configuration $config)
    {
        $this->config = $config;
        $this->qualityScanner = new QualityScanner($config);
        $this->securityScanner = new SecurityScanner($config);
        $this->dependencyScanner = new DependencyScanner($config);
        $this->coverageScanner = new CoverageScanner($config);
        $this->analyzer = new ResultAnalyzer($config);
    }

    /**
     * Scan a project directory
     */
    public function scan(string $projectPath, array $options = []): array
    {
        $results = [
            'project_path' => $projectPath,
            'scan_time' => date('Y-m-d H:i:s'),
            'options' => $options,
            'quality' => [],
            'security' => [],
            'dependencies' => [],
            'coverage' => [],
            'summary' => []
        ];

        // Quality analysis
        if (!($options['skip_quality'] ?? false)) {
            $results['quality'] = $this->qualityScanner->scan($projectPath, $options);
        }

        // Security analysis
        if (!($options['skip_security'] ?? false)) {
            $results['security'] = $this->securityScanner->scan($projectPath, $options);
        }

        // Dependency analysis
        if (!($options['skip_dependencies'] ?? false)) {
            $results['dependencies'] = $this->dependencyScanner->scan($projectPath, $options);
        }

        // Coverage analysis
        if (!($options['skip_coverage'] ?? false)) {
            $results['coverage'] = $this->coverageScanner->scan($projectPath, $options);
        }

        // Analyze and summarize results
        $results['summary'] = $this->analyzer->analyze($results);

        return $results;
    }

    /**
     * Generate reports in specified formats
     */
    public function generateReports(array $results, string $outputDir, array $formats): void
    {
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }

        foreach ($formats as $format) {
            switch (strtolower($format)) {
                case 'json':
                    $reporter = new JsonReporter();
                    $reporter->generate($results, $outputDir);
                    break;

                case 'html':
                    $reporter = new HtmlReporter($this->config);
                    $reporter->generate($results, $outputDir);
                    break;

                case 'console':
                    $reporter = new ConsoleReporter();
                    $reporter->generate($results, $outputDir);
                    break;

                default:
                    throw new \InvalidArgumentException("Unsupported format: {$format}");
            }
        }
    }
}
