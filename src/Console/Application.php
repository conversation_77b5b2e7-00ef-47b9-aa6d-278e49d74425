<?php

declare(strict_types=1);

namespace Z<PERSON>eam\LaravelChecker\Console;

use Symfony\Component\Console\Application as BaseApplication;
use ZTeam\LaravelChecker\Console\Command\ScanCommand;

/**
 * Laravel Checker Console Application
 * 
 * Main console application that registers all available commands.
 */
class Application extends BaseApplication
{
    private const NAME = 'Laravel Checker';
    private const VERSION = '1.0.0';

    public function __construct()
    {
        parent::__construct(self::NAME, self::VERSION);
        
        $this->addCommands([
            new ScanCommand(),
        ]);
        
        // Set the default command to scan
        $this->setDefaultCommand('scan', true);
    }
}
