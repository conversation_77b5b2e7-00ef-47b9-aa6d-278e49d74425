<?php

declare(strict_types=1);

namespace ZTeam\LaravelChecker\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use ZTeam\LaravelChecker\LaravelChecker;
use ZTeam\LaravelChecker\Config\Configuration;

/**
 * Main scan command for the Laravel Checker tool
 */
class ScanCommand extends Command
{
    protected static $defaultName = 'scan';
    protected static $defaultDescription = 'Scan a project for quality, security, and release readiness';

    protected function configure(): void
    {
        $this
            ->setDescription(self::$defaultDescription)
            ->addArgument(
                'path',
                InputArgument::OPTIONAL,
                'Path to the project directory to scan',
                getcwd()
            )
            ->addOption(
                'config',
                'c',
                InputOption::VALUE_REQUIRED,
                'Path to configuration file'
            )
            ->addOption(
                'output',
                'o',
                InputOption::VALUE_REQUIRED,
                'Output directory for reports',
                './reports'
            )
            ->addOption(
                'format',
                'f',
                InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
                'Output format(s): json, html, console',
                ['console', 'json']
            )
            ->addOption(
                'exclude',
                null,
                InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY,
                'Directories to exclude from scanning',
                []
            )
            ->addOption(
                'skip-security',
                null,
                InputOption::VALUE_NONE,
                'Skip security vulnerability scanning'
            )
            ->addOption(
                'skip-quality',
                null,
                InputOption::VALUE_NONE,
                'Skip code quality analysis'
            )
            ->addOption(
                'skip-dependencies',
                null,
                InputOption::VALUE_NONE,
                'Skip dependency analysis'
            )
            ->addOption(
                'skip-coverage',
                null,
                InputOption::VALUE_NONE,
                'Skip test coverage analysis'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $projectPath = $input->getArgument('path');
        $configPath = $input->getOption('config');
        $outputDir = $input->getOption('output');
        $formats = $input->getOption('format');
        $excludeDirs = $input->getOption('exclude');
        
        // Validate project path
        if (!is_dir($projectPath)) {
            $io->error("Project path does not exist: {$projectPath}");
            return Command::FAILURE;
        }

        $io->title('Laravel Checker - Project Scanner');
        $io->text("Scanning project: <info>{$projectPath}</info>");

        try {
            // Load configuration
            $config = new Configuration($configPath);
            
            // Configure scan options
            $scanOptions = [
                'exclude_dirs' => array_merge($config->getExcludeDirs(), $excludeDirs),
                'skip_security' => $input->getOption('skip-security'),
                'skip_quality' => $input->getOption('skip-quality'),
                'skip_dependencies' => $input->getOption('skip-dependencies'),
                'skip_coverage' => $input->getOption('skip-coverage'),
            ];

            // Initialize and run scanner
            $checker = new LaravelChecker($config);
            $results = $checker->scan($projectPath, $scanOptions);

            // Generate reports
            $checker->generateReports($results, $outputDir, $formats);

            // Display summary
            $this->displaySummary($io, $results);

            $io->success('Scan completed successfully!');
            $io->text("Reports generated in: <info>{$outputDir}</info>");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error("Scan failed: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function displaySummary(SymfonyStyle $io, array $results): void
    {
        $io->section('Scan Summary');
        
        $summary = $results['summary'] ?? [];
        
        $io->table(
            ['Metric', 'Value', 'Status'],
            [
                ['Quality Score', $summary['quality_score'] ?? 'N/A', $this->getScoreStatus($summary['quality_score'] ?? 0)],
                ['Security Issues', $summary['security_issues'] ?? 0, $this->getSecurityStatus($summary['security_issues'] ?? 0)],
                ['Code Issues', $summary['code_issues'] ?? 0, $this->getIssueStatus($summary['code_issues'] ?? 0)],
                ['Outdated Dependencies', $summary['outdated_deps'] ?? 0, $this->getDependencyStatus($summary['outdated_deps'] ?? 0)],
                ['Test Coverage', ($summary['test_coverage'] ?? 0) . '%', $this->getCoverageStatus($summary['test_coverage'] ?? 0)],
            ]
        );
    }

    private function getScoreStatus(float $score): string
    {
        if ($score >= 80) return '<fg=green>Good</>';
        if ($score >= 60) return '<fg=yellow>Fair</>';
        return '<fg=red>Poor</>';
    }

    private function getSecurityStatus(int $issues): string
    {
        if ($issues === 0) return '<fg=green>Clean</>';
        if ($issues <= 5) return '<fg=yellow>Warning</>';
        return '<fg=red>Critical</>';
    }

    private function getIssueStatus(int $issues): string
    {
        if ($issues <= 10) return '<fg=green>Good</>';
        if ($issues <= 50) return '<fg=yellow>Fair</>';
        return '<fg=red>Poor</>';
    }

    private function getDependencyStatus(int $deps): string
    {
        if ($deps === 0) return '<fg=green>Up to date</>';
        if ($deps <= 3) return '<fg=yellow>Some outdated</>';
        return '<fg=red>Many outdated</>';
    }

    private function getCoverageStatus(float $coverage): string
    {
        if ($coverage >= 80) return '<fg=green>Good</>';
        if ($coverage >= 60) return '<fg=yellow>Fair</>';
        return '<fg=red>Poor</>';
    }
}
