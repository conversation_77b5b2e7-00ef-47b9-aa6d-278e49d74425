{"name": "test-laravel-project", "version": "1.0.0", "description": "Test Laravel project for <PERSON><PERSON> Checker demo", "main": "resources/js/app.js", "scripts": {"dev": "vite", "build": "vite build", "test": "jest", "test:coverage": "jest --coverage"}, "dependencies": {"vue": "^2.6.14", "axios": "^0.21.0", "lodash": "^4.17.19", "bootstrap": "^5.1.3"}, "devDependencies": {"vite": "^4.0.0", "jest": "^29.0.0", "eslint": "^8.0.0", "@babel/core": "^7.17.0", "@babel/preset-env": "^7.17.0"}, "jest": {"testEnvironment": "jsdom", "collectCoverageFrom": ["resources/js/**/*.js", "!resources/js/vendor/**"]}}