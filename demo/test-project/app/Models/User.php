<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * User model with some intentional issues for testing
 */
class User extends Model
{
    protected $fillable = ['name', 'email', 'password'];

    // This method has high complexity (intentional issue)
    public function complexMethod($param1, $param2, $param3, $param4, $param5, $param6)
    {
        if ($param1 > 0) {
            if ($param2 < 10) {
                for ($i = 0; $i < $param1; $i++) {
                    if ($i % 2 === 0) {
                        if ($i > 5) {
                            while ($i < 8) {
                                switch ($i) {
                                    case 6:
                                        if ($param3) {
                                            if ($param4) {
                                                return "complex result";
                                            }
                                        }
                                        break;
                                    case 7:
                                        return "seven";
                                    default:
                                        $i++;
                                }
                            }
                        }
                    }
                }
            }
        }
        return "default";
    }

    // SQL Injection vulnerability (intentional)
    public function findByIdUnsafe($id)
    {
        return DB::select("SELECT * FROM users WHERE id = " . $id);
    }

    // Long method (intentional issue)
    public function veryLongMethod()
    {
        $result = '';
        $result .= 'Line 1';
        $result .= 'Line 2';
        $result .= 'Line 3';
        $result .= 'Line 4';
        $result .= 'Line 5';
        $result .= 'Line 6';
        $result .= 'Line 7';
        $result .= 'Line 8';
        $result .= 'Line 9';
        $result .= 'Line 10';
        $result .= 'Line 11';
        $result .= 'Line 12';
        $result .= 'Line 13';
        $result .= 'Line 14';
        $result .= 'Line 15';
        $result .= 'Line 16';
        $result .= 'Line 17';
        $result .= 'Line 18';
        $result .= 'Line 19';
        $result .= 'Line 20';
        $result .= 'Line 21';
        $result .= 'Line 22';
        $result .= 'Line 23';
        $result .= 'Line 24';
        $result .= 'Line 25';
        $result .= 'Line 26';
        $result .= 'Line 27';
        $result .= 'Line 28';
        $result .= 'Line 29';
        $result .= 'Line 30';
        $result .= 'Line 31';
        $result .= 'Line 32';
        $result .= 'Line 33';
        $result .= 'Line 34';
        $result .= 'Line 35';
        $result .= 'Line 36';
        $result .= 'Line 37';
        $result .= 'Line 38';
        $result .= 'Line 39';
        $result .= 'Line 40';
        $result .= 'Line 41';
        $result .= 'Line 42';
        $result .= 'Line 43';
        $result .= 'Line 44';
        $result .= 'Line 45';
        $result .= 'Line 46';
        $result .= 'Line 47';
        $result .= 'Line 48';
        $result .= 'Line 49';
        $result .= 'Line 50';
        $result .= 'Line 51';
        $result .= 'Line 52';
        $result .= 'Line 53';
        $result .= 'Line 54';
        $result .= 'Line 55';
        $result .= 'Line 56';
        $result .= 'Line 57';
        $result .= 'Line 58';
        $result .= 'Line 59';
        $result .= 'Line 60';
        
        return $result;
    }

    // XSS vulnerability (intentional)
    public function displayUserBio($bio)
    {
        echo $bio; // Should use htmlspecialchars()
    }
}
