// Sample JavaScript file with some issues

// Hardcoded API key (security issue)
const API_KEY = 'sk-1234567890abcdef';

// XSS vulnerability
function displayUserInput(input) {
    document.getElementById('output').innerHTML = input; // Should use textContent
}

// Insecure random (security issue)
function generateToken() {
    return Math.random().toString(36).substring(2);
}

// Complex function (quality issue)
function complexFunction(a, b, c, d, e, f) {
    if (a > 0) {
        if (b < 10) {
            for (let i = 0; i < a; i++) {
                if (i % 2 === 0) {
                    if (i > 5) {
                        while (i < 8) {
                            switch (i) {
                                case 6:
                                    if (c) {
                                        if (d) {
                                            if (e) {
                                                if (f) {
                                                    return 'very complex';
                                                }
                                            }
                                        }
                                    }
                                    break;
                                case 7:
                                    return 'seven';
                                default:
                                    i++;
                            }
                        }
                    }
                }
            }
        }
    }
    return 'default';
}

// Unused variable (quality issue)
function unusedVariableFunction() {
    const unusedVar = 'this variable is never used';
    const usedVar = 'this is used';
    return usedVar;
}

// Missing error handling
function riskyFunction() {
    const data = JSON.parse(localStorage.getItem('userData'));
    return data.name.toUpperCase();
}

// Long function (quality issue)
function veryLongFunction() {
    let result = '';
    result += 'Line 1\n';
    result += 'Line 2\n';
    result += 'Line 3\n';
    result += 'Line 4\n';
    result += 'Line 5\n';
    result += 'Line 6\n';
    result += 'Line 7\n';
    result += 'Line 8\n';
    result += 'Line 9\n';
    result += 'Line 10\n';
    result += 'Line 11\n';
    result += 'Line 12\n';
    result += 'Line 13\n';
    result += 'Line 14\n';
    result += 'Line 15\n';
    result += 'Line 16\n';
    result += 'Line 17\n';
    result += 'Line 18\n';
    result += 'Line 19\n';
    result += 'Line 20\n';
    result += 'Line 21\n';
    result += 'Line 22\n';
    result += 'Line 23\n';
    result += 'Line 24\n';
    result += 'Line 25\n';
    result += 'Line 26\n';
    result += 'Line 27\n';
    result += 'Line 28\n';
    result += 'Line 29\n';
    result += 'Line 30\n';
    result += 'Line 31\n';
    result += 'Line 32\n';
    result += 'Line 33\n';
    result += 'Line 34\n';
    result += 'Line 35\n';
    result += 'Line 36\n';
    result += 'Line 37\n';
    result += 'Line 38\n';
    result += 'Line 39\n';
    result += 'Line 40\n';
    result += 'Line 41\n';
    result += 'Line 42\n';
    result += 'Line 43\n';
    result += 'Line 44\n';
    result += 'Line 45\n';
    result += 'Line 46\n';
    result += 'Line 47\n';
    result += 'Line 48\n';
    result += 'Line 49\n';
    result += 'Line 50\n';
    result += 'Line 51\n';
    result += 'Line 52\n';
    result += 'Line 53\n';
    result += 'Line 54\n';
    result += 'Line 55\n';
    result += 'Line 56\n';
    result += 'Line 57\n';
    result += 'Line 58\n';
    result += 'Line 59\n';
    result += 'Line 60\n';
    
    return result;
}

export { complexFunction, generateToken, displayUserInput };
