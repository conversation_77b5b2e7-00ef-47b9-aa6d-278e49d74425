#!/bin/bash

# Laravel Checker Demo Script
# This script demonstrates the Laravel Checker tool

echo "🚀 Laravel Checker Demo"
echo "======================="
echo ""

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed. Please install PHP 8.0 or higher."
    echo ""
    echo "📋 Installation commands:"
    echo "   macOS:    brew install php composer"
    echo "   Ubuntu:   sudo apt install php8.1-cli php8.1-mbstring php8.1-xml composer"
    echo "   CentOS:   sudo yum install php php-cli composer"
    echo "   Windows:  Download from https://windows.php.net/download/"
    echo ""
    echo "🔧 Or run the requirements check script:"
    echo "   ./scripts/check-requirements.sh"
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "✅ PHP Version: $PHP_VERSION"

# Check if Composer is available
if ! command -v composer &> /dev/null; then
    echo "❌ Composer is not installed. Please install Composer."
    echo "   Visit: https://getcomposer.org/download/"
    exit 1
fi

echo "✅ Composer is available"
echo ""

# Install dependencies
echo "📦 Installing dependencies..."
if [ ! -d "vendor" ]; then
    composer install --no-dev --quiet
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed"
else
    echo "✅ Dependencies already installed"
fi

echo ""

# Create reports directory
mkdir -p demo/reports

# Run Laravel Checker on demo project
echo "🔍 Running Laravel Checker on demo project..."
echo "Project path: $(pwd)/demo/test-project"
echo ""

# Run with different formats
echo "Generating reports..."

# Console format
echo "📄 Console Report:"
echo "=================="
php bin/laravel-checker demo/test-project --format=console --output=demo/reports

echo ""
echo "📊 JSON Report generated: demo/reports/laravel-checker-report.json"
echo "🌐 HTML Report generated: demo/reports/laravel-checker-report.html"
echo ""

# Show summary if JSON report exists
if [ -f "demo/reports/summary.json" ]; then
    echo "📋 Summary:"
    echo "==========="
    
    # Extract key metrics using basic tools (since jq might not be available)
    if command -v python3 &> /dev/null; then
        python3 -c "
import json
try:
    with open('demo/reports/summary.json', 'r') as f:
        data = json.load(f)
    print(f\"Overall Score: {data.get('overall_score', 0):.1f}/100\")
    print(f\"Quality Score: {data.get('quality_score', 0):.1f}/100\")
    print(f\"Security Score: {data.get('security_score', 0):.1f}/100\")
    print(f\"Test Coverage: {data.get('test_coverage', 0):.1f}%\")
    print(f\"Code Issues: {data.get('code_issues', 0)}\")
    print(f\"Security Issues: {data.get('security_issues', 0)}\")
    print(f\"Release Readiness: {data.get('release_readiness', 'unknown')}\")
except Exception as e:
    print(f\"Could not parse summary: {e}\")
"
    else
        echo "Summary available in demo/reports/summary.json"
    fi
fi

echo ""
echo "🎉 Demo completed!"
echo ""
echo "📁 Generated files:"
echo "   - demo/reports/laravel-checker-report.json"
echo "   - demo/reports/laravel-checker-report.html"
echo "   - demo/reports/console-report.txt"
echo "   - demo/reports/summary.json"
echo ""
echo "💡 To view the HTML report:"
echo "   open demo/reports/laravel-checker-report.html"
echo ""
echo "🔧 To run on your own project:"
echo "   ./bin/laravel-checker /path/to/your/project"
echo ""
echo "📖 For more options:"
echo "   ./bin/laravel-checker --help"
