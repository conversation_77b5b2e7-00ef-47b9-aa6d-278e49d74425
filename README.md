# Lara<PERSON> Checker

Một công cụ quét dự án web toàn diện để đánh giá chất lượng mã, bảo mật và sẵn sàng release cho các dự án <PERSON>, PHP, JavaScript, Vue.js và Nuxt.js.

## 🚀 Tính năng chính

- **Phân tích chất lượng mã**: Quét cú pháp, code smells, complexity, và coding standards
- **Kiểm tra bảo mật**: Phát hiện lỗ hổng SQL injection, XSS, CSRF và các vấn đề bảo mật khác
- **Phân tích dependencies**: Kiểm tra packages lỗi thời và có lỗ hổng bảo mật
- **Đánh giá test coverage**: Tính toán độ bao phủ kiểm thử
- **Báo cáo đa định dạng**: JSON, HTML và Console
- **T<PERSON>ch hợp CI/CD**: <PERSON><PERSON> dàng tích hợp vào pipeline

## 📋 Yêu cầu hệ thống

- PHP 8.0 hoặc cao hơn
- Composer
- Node.js và NPM (tù<PERSON> chọn, cho phân tích JavaScript)

### Công cụ bên ngoài (tùy chọn)

- **PHP**: phpcs, phpstan, psalm
- **JavaScript**: eslint, jest
- **Dependencies**: composer audit, npm audit

## 🛠 Cài đặt

### Cài đặt tự động (Khuyến nghị)

```bash
# Clone repository
git clone https://github.com/zteam/laravel-checker.git
cd laravel-checker

# Chạy script cài đặt tự động
./scripts/install.sh
```

### Cài đặt thủ công

#### 1. Kiểm tra yêu cầu hệ thống
```bash
./scripts/check-requirements.sh
```

#### 2. Cài đặt dependencies
```bash
# Cài đặt đầy đủ
composer install

# Hoặc cài đặt tối thiểu nếu gặp lỗi
cp composer-minimal.json composer.json
composer install
```

#### 3. Cấp quyền thực thi
```bash
chmod +x bin/laravel-checker
```

### 3. Cài đặt global

```bash
composer global require zteam/laravel-checker
```

## 🎯 Sử dụng cơ bản

### Quét dự án hiện tại

```bash
./bin/laravel-checker
```

### Quét một thư mục cụ thể

```bash
./bin/laravel-checker /path/to/project
```

### Tùy chọn đầu ra

```bash
# Tạo báo cáo JSON và HTML
./bin/laravel-checker --format=json --format=html --output=./reports

# Chỉ tạo báo cáo console
./bin/laravel-checker --format=console
```

### Bỏ qua một số loại kiểm tra

```bash
# Bỏ qua kiểm tra bảo mật
./bin/laravel-checker --skip-security

# Bỏ qua kiểm tra coverage
./bin/laravel-checker --skip-coverage
```

### Sử dụng file cấu hình tùy chỉnh

```bash
./bin/laravel-checker --config=./my-config.json
```

## ⚙️ Cấu hình

Tạo file `config.json` để tùy chỉnh quy tắc kiểm tra:

```json
{
    "exclude_dirs": [
        "vendor",
        "node_modules",
        ".git",
        "storage/logs"
    ],
    "quality_rules": {
        "max_complexity": 10,
        "max_method_length": 50,
        "min_test_coverage": 70
    },
    "security_rules": {
        "check_sql_injection": true,
        "check_xss": true,
        "check_csrf": true
    },
    "tools": {
        "phpcs": {
            "enabled": true,
            "standard": "PSR12"
        },
        "phpstan": {
            "enabled": true,
            "level": 6
        }
    }
}
```

## 📊 Định dạng báo cáo

### 1. Console Report
Hiển thị tóm tắt trực tiếp trên terminal với màu sắc và biểu tượng.

### 2. JSON Report
```json
{
    "summary": {
        "overall_score": 85.2,
        "quality_score": 78.5,
        "security_score": 95.0,
        "coverage_score": 82.3,
        "release_readiness": "ready"
    },
    "quality": {
        "total_files": 150,
        "total_lines": 12500,
        "issues": []
    }
}
```

### 3. HTML Report
Báo cáo tương tác với biểu đồ, bảng và khả năng lọc kết quả.

## 🔧 Tích hợp CI/CD

### GitHub Actions

```yaml
name: Code Quality Check
on: [push, pull_request]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
          
      - name: Install dependencies
        run: composer install
        
      - name: Run Laravel Checker
        run: |
          composer global require zteam/laravel-checker
          laravel-checker --format=json --output=./reports
          
      - name: Upload reports
        uses: actions/upload-artifact@v2
        with:
          name: quality-reports
          path: reports/
```

### Jenkins Pipeline

```groovy
pipeline {
    agent any
    
    stages {
        stage('Quality Check') {
            steps {
                sh 'composer install'
                sh 'composer global require zteam/laravel-checker'
                sh 'laravel-checker --format=json --output=./reports'
                
                publishHTML([
                    allowMissing: false,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: 'reports',
                    reportFiles: 'laravel-checker-report.html',
                    reportName: 'Quality Report'
                ])
            }
        }
    }
}
```

## 📈 Metrics và Scoring

### Overall Score
Điểm tổng thể được tính từ trọng số của các metrics:
- Quality Score: 30%
- Security Score: 30%
- Coverage Score: 20%
- Dependency Score: 20%

### Quality Score
Dựa trên:
- Số lượng issues per file
- Cyclomatic complexity
- Code standards compliance
- Method/class length

### Security Score
Dựa trên:
- Số lượng vulnerabilities
- Severity level
- Security best practices

### Release Readiness
- **Ready**: Tất cả gates đều pass
- **Ready with Warnings**: Một số warnings nhưng không có blockers
- **Not Ready**: Có critical issues hoặc security vulnerabilities

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📝 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 🔧 Xử lý lỗi thường gặp

### Lỗi "psalm/psalm ^5.0, it could not be found"
```bash
# Package name không đúng, sửa thành:
composer require --dev "vimeo/psalm:^5.0"

# Hoặc sử dụng cài đặt tối thiểu:
cp composer-minimal.json composer.json
composer install
```

### Lỗi "PHP not found"
```bash
# Cài đặt PHP theo hệ điều hành:
# macOS: brew install php
# Ubuntu: sudo apt install php8.1-cli
# CentOS: sudo yum install php

# Kiểm tra cài đặt:
./scripts/check-requirements.sh
```

### Lỗi dependencies conflict
```bash
# Clear cache và thử lại:
composer clear-cache
composer install

# Hoặc sử dụng cài đặt tự động:
./scripts/install.sh
```

## 🆘 Hỗ trợ

- **Issues**: [GitHub Issues](https://github.com/zteam/laravel-checker/issues)
- **Documentation**: [Wiki](https://github.com/zteam/laravel-checker/wiki)
- **Email**: <EMAIL>

## 🔄 Changelog

### v1.0.0
- Initial release
- PHP và JavaScript analysis
- Security vulnerability scanning
- Dependency checking
- Test coverage analysis
- Multiple report formats
- CI/CD integration support
