# Laravel Checker - Troubleshooting Guide

## 🔧 Lỗi cài đặt (Installation Issues)

### 1. Lỗi "psalm/psalm ^5.0, it could not be found"

**Nguyên nhân**: Package name không chính xác trong composer.json

**Giải pháp**:
```bash
# Sửa package name từ "psalm/psalm" thành "vimeo/psalm"
composer require --dev "vimeo/psalm:^5.0"

# Hoặc sử dụng cài đặt tối thiểu
cp composer-minimal.json composer.json
composer install
```

### 2. Lỗi "env: php: No such file or directory"

**Nguyên nhân**: PHP chưa được cài đặt hoặc không có trong PATH

**Giải pháp**:
```bash
# Kiểm tra PHP
which php
php --version

# Cài đặt PHP theo hệ điều hành:

# macOS
brew install php

# Ubuntu/Debian
sudo apt update
sudo apt install php8.1-cli php8.1-mbstring php8.1-xml php8.1-curl

# CentOS/RHEL
sudo yum install php php-cli php-mbstring php-xml

# Kiểm tra lại
./scripts/check-requirements.sh
```

### 3. Lỗi "Composer not found"

**Giải pháp**:
```bash
# Cài đặt Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Hoặc trên Windows, download từ: https://getcomposer.org/download/
```

### 4. Lỗi "Call to undefined method PhpParser\ParserFactory::create()"

**Nguyên nhân**: Phiên bản PhpParser không tương thích

**Giải pháp**:
```bash
# Cập nhật PhpParser
composer require "nikic/php-parser:^4.15|^5.0"

# Hoặc test PhpParser
php scripts/test-phpparser.php

# Kiểm tra version hiện tại
composer show nikic/php-parser
```

### 5. Lỗi dependencies conflict

**Giải pháp**:
```bash
# Clear cache
composer clear-cache

# Update dependencies
composer update

# Hoặc cài đặt với ignore platform requirements
composer install --ignore-platform-reqs

# Sử dụng cài đặt tự động
./scripts/install.sh
```

## 🚀 Lỗi runtime (Runtime Issues)

### 1. Lỗi "Permission denied"

**Giải pháp**:
```bash
# Cấp quyền thực thi
chmod +x bin/laravel-checker
chmod +x scripts/*.sh

# Hoặc chạy với php
php bin/laravel-checker
```

### 2. Lỗi "Class not found"

**Giải pháp**:
```bash
# Regenerate autoloader
composer dump-autoload

# Kiểm tra cài đặt
composer validate
composer diagnose
```

### 3. Lỗi "Memory limit exceeded"

**Giải pháp**:
```bash
# Tăng memory limit
php -d memory_limit=512M bin/laravel-checker

# Hoặc chỉnh sửa php.ini
memory_limit = 512M

# Kiểm tra memory limit hiện tại
php -r "echo ini_get('memory_limit');"
```

### 4. Lỗi "Maximum execution time exceeded"

**Giải pháp**:
```bash
# Tăng execution time
php -d max_execution_time=300 bin/laravel-checker

# Hoặc chỉnh sửa php.ini
max_execution_time = 300
```

## 📊 Lỗi scanning (Scanning Issues)

### 1. Lỗi "No files found to scan"

**Nguyên nhân**: Thư mục không chứa file PHP/JS hoặc bị exclude

**Giải pháp**:
```bash
# Kiểm tra thư mục
ls -la /path/to/project

# Kiểm tra cấu hình exclude
cat .laravel-checker.json

# Chạy với verbose để debug
./bin/laravel-checker --verbose
```

### 2. Lỗi "External tool not found"

**Nguyên nhân**: phpcs, phpstan, eslint chưa được cài đặt

**Giải pháp**:
```bash
# Cài đặt PHP tools
composer global require squizlabs/php_codesniffer
composer global require phpstan/phpstan

# Cài đặt JS tools
npm install -g eslint

# Kiểm tra PATH
echo $PATH
which phpcs
which eslint
```

### 3. Lỗi "Parse error in PHP file"

**Giải pháp**:
```bash
# Kiểm tra syntax file bị lỗi
php -l /path/to/file.php

# Bỏ qua file có lỗi syntax
./bin/laravel-checker --exclude=problematic-directory
```

## 📝 Lỗi báo cáo (Report Issues)

### 1. Lỗi "Cannot write to output directory"

**Giải pháp**:
```bash
# Tạo thư mục output
mkdir -p ./reports

# Cấp quyền ghi
chmod 755 ./reports

# Kiểm tra quyền
ls -la ./reports
```

### 2. Lỗi "HTML report generation failed"

**Nguyên nhân**: Thiếu Twig template engine

**Giải pháp**:
```bash
# Cài đặt Twig
composer require twig/twig

# Hoặc chỉ tạo JSON report
./bin/laravel-checker --format=json
```

### 3. Lỗi "Invalid JSON in report"

**Giải pháp**:
```bash
# Kiểm tra JSON syntax
cat reports/report.json | python -m json.tool

# Hoặc sử dụng jq
cat reports/report.json | jq .
```

## 🔍 Debug và Diagnostics

### 1. Chạy với debug mode

```bash
# Verbose output
./bin/laravel-checker --verbose

# Debug mode (nếu có)
./bin/laravel-checker --debug

# Kiểm tra cấu hình
./bin/laravel-checker --show-config
```

### 2. Kiểm tra log files

```bash
# Kiểm tra PHP error log
tail -f /var/log/php_errors.log

# Hoặc enable error reporting
php -d display_errors=1 -d error_reporting=E_ALL bin/laravel-checker
```

### 3. Test với dự án mẫu

```bash
# Chạy demo để test
./demo/run-demo.sh

# Test với dự án đơn giản
mkdir test-project
echo '<?php echo "Hello World";' > test-project/test.php
./bin/laravel-checker test-project
```

## 🌐 Lỗi CI/CD (CI/CD Issues)

### 1. GitHub Actions fails

**Giải pháp**:
```yaml
# Thêm vào workflow
- name: Install PHP
  uses: shivammathur/setup-php@v2
  with:
    php-version: '8.1'
    extensions: mbstring, xml, curl

- name: Install dependencies
  run: composer install --no-dev --prefer-dist
```

### 2. Jenkins pipeline fails

**Giải pháp**:
```groovy
// Thêm vào pipeline
stage('Setup') {
    steps {
        sh 'php --version'
        sh 'composer --version'
        sh 'composer install --no-dev'
    }
}
```

## 📞 Nhận hỗ trợ

### 1. Thu thập thông tin

Khi báo cáo lỗi, hãy cung cấp:

```bash
# Thông tin hệ thống
php --version
composer --version
uname -a

# Thông tin lỗi
./bin/laravel-checker --verbose 2>&1 | tee error.log

# Cấu hình
cat .laravel-checker.json
```

### 2. Liên hệ hỗ trợ

- **GitHub Issues**: https://github.com/zteam/laravel-checker/issues
- **Email**: <EMAIL>
- **Documentation**: README.md, INSTALL.md, USAGE.md

### 3. Template báo cáo lỗi

```
**Mô tả lỗi:**
[Mô tả chi tiết lỗi]

**Môi trường:**
- OS: [macOS/Linux/Windows]
- PHP Version: [8.1.x]
- Composer Version: [2.x.x]

**Các bước tái tạo:**
1. [Bước 1]
2. [Bước 2]
3. [Bước 3]

**Kết quả mong đợi:**
[Mô tả kết quả mong đợi]

**Kết quả thực tế:**
[Mô tả kết quả thực tế]

**Log lỗi:**
```
[Paste error log here]
```

**Cấu hình:**
```json
[Paste .laravel-checker.json content]
```
```

Với hướng dẫn này, bạn có thể xử lý hầu hết các lỗi thường gặp khi sử dụng Laravel Checker.
