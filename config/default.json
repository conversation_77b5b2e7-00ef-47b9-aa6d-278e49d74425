{"exclude_dirs": ["vendor", "node_modules", ".git", "storage/logs", "storage/cache", "bootstrap/cache", "public/storage", "tests/coverage", ".phpunit.cache"], "file_extensions": {"php": ["php"], "javascript": ["js", "jsx", "ts", "tsx"], "vue": ["vue"], "css": ["css", "scss", "sass", "less"], "html": ["html", "blade.php"]}, "quality_rules": {"max_complexity": 10, "max_method_length": 50, "max_class_length": 500, "min_test_coverage": 70, "max_nesting_level": 4, "max_parameters": 5}, "security_rules": {"check_sql_injection": true, "check_xss": true, "check_csrf": true, "check_file_upload": true, "check_authentication": true, "check_authorization": true, "check_input_validation": true, "check_output_encoding": true}, "dependency_rules": {"check_outdated": true, "check_vulnerabilities": true, "max_outdated_days": 365, "severity_threshold": "medium"}, "coverage_rules": {"min_line_coverage": 70, "min_branch_coverage": 60, "min_function_coverage": 80}, "tools": {"phpcs": {"enabled": true, "standard": "PSR12"}, "phpstan": {"enabled": true, "level": 6}, "eslint": {"enabled": true, "config": ".eslintrc.js"}, "npm_audit": {"enabled": true}, "composer_audit": {"enabled": true}}}