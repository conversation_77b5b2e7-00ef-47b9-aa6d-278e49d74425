#!/bin/bash

# Laravel Checker Requirements Check Script
# This script checks if all required dependencies are installed

echo "🔍 Laravel Checker - Requirements Check"
echo "======================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Track if all requirements are met
ALL_GOOD=true

# Check PHP
echo "📋 Checking PHP..."
if command -v php &> /dev/null; then
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    PHP_MAJOR=$(php -r "echo PHP_MAJOR_VERSION;")
    
    if [ "$PHP_MAJOR" -ge 8 ]; then
        echo -e "  ✅ PHP $PHP_VERSION ${GREEN}(OK)${NC}"
    else
        echo -e "  ❌ PHP $PHP_VERSION ${RED}(Requires PHP 8.0+)${NC}"
        ALL_GOOD=false
    fi
else
    echo -e "  ❌ PHP ${RED}(Not installed)${NC}"
    echo -e "     ${YELLOW}Install PHP:${NC}"
    echo "     - macOS: brew install php"
    echo "     - Ubuntu: sudo apt install php8.1-cli php8.1-mbstring php8.1-xml"
    echo "     - CentOS: sudo yum install php php-cli php-mbstring php-xml"
    ALL_GOOD=false
fi

echo ""

# Check Composer
echo "📦 Checking Composer..."
if command -v composer &> /dev/null; then
    COMPOSER_VERSION=$(composer --version --no-ansi | head -n1)
    echo -e "  ✅ $COMPOSER_VERSION ${GREEN}(OK)${NC}"
else
    echo -e "  ❌ Composer ${RED}(Not installed)${NC}"
    echo -e "     ${YELLOW}Install Composer:${NC}"
    echo "     curl -sS https://getcomposer.org/installer | php"
    echo "     sudo mv composer.phar /usr/local/bin/composer"
    ALL_GOOD=false
fi

echo ""

# Check PHP Extensions
echo "🔧 Checking PHP Extensions..."
if command -v php &> /dev/null; then
    REQUIRED_EXTENSIONS=("mbstring" "xml" "json" "tokenizer" "ctype")
    
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if php -m | grep -q "^$ext$"; then
            echo -e "  ✅ $ext ${GREEN}(OK)${NC}"
        else
            echo -e "  ❌ $ext ${RED}(Missing)${NC}"
            ALL_GOOD=false
        fi
    done
fi

echo ""

# Check Node.js (Optional)
echo "🟡 Checking Node.js (Optional for JavaScript analysis)..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "  ✅ Node.js $NODE_VERSION ${GREEN}(OK)${NC}"
    
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        echo -e "  ✅ NPM $NPM_VERSION ${GREEN}(OK)${NC}"
    else
        echo -e "  ⚠️  NPM ${YELLOW}(Not found)${NC}"
    fi
else
    echo -e "  ⚠️  Node.js ${YELLOW}(Not installed - JavaScript analysis will be limited)${NC}"
    echo "     Install Node.js: https://nodejs.org/"
fi

echo ""

# Check Git (Optional)
echo "🟡 Checking Git (Optional)..."
if command -v git &> /dev/null; then
    GIT_VERSION=$(git --version)
    echo -e "  ✅ $GIT_VERSION ${GREEN}(OK)${NC}"
else
    echo -e "  ⚠️  Git ${YELLOW}(Not installed)${NC}"
fi

echo ""

# Summary
echo "📊 Summary"
echo "=========="
if [ "$ALL_GOOD" = true ]; then
    echo -e "${GREEN}✅ All required dependencies are installed!${NC}"
    echo ""
    echo "🚀 You can now install Laravel Checker:"
    echo "   composer install"
    echo ""
    echo "🎯 Or run the demo:"
    echo "   ./demo/run-demo.sh"
else
    echo -e "${RED}❌ Some required dependencies are missing.${NC}"
    echo ""
    echo "📋 Please install the missing dependencies and run this script again."
    echo ""
    echo "💡 Quick install commands:"
    echo ""
    echo "🍎 macOS:"
    echo "   brew install php composer"
    echo ""
    echo "🐧 Ubuntu/Debian:"
    echo "   sudo apt update"
    echo "   sudo apt install php8.1-cli php8.1-mbstring php8.1-xml php8.1-curl composer"
    echo ""
    echo "🎩 CentOS/RHEL:"
    echo "   sudo yum install php php-cli php-mbstring php-xml composer"
    echo ""
    echo "🪟 Windows:"
    echo "   Download PHP from: https://windows.php.net/download/"
    echo "   Download Composer from: https://getcomposer.org/download/"
fi

echo ""
echo "📚 For detailed installation instructions, see: INSTALL.md"
echo ""

# Exit with appropriate code
if [ "$ALL_GOOD" = true ]; then
    exit 0
else
    exit 1
fi
