<?php

/**
 * Test script for DependencyScanner
 */

require_once __DIR__ . '/../vendor/autoload.php';

echo "🧪 Testing DependencyScanner\n";
echo "============================\n\n";

use ZTeam\LaravelChecker\Config\Configuration;
use ZTeam\LaravelChecker\Scanner\DependencyScanner;

try {
    // Create test configuration
    $config = new Configuration();
    $scanner = new DependencyScanner($config);
    
    echo "✅ DependencyScanner created successfully\n\n";
    
    // Test with current project
    echo "🔍 Testing with current project...\n";
    $results = $scanner->scan(__DIR__ . '/..');
    
    echo "📊 Results:\n";
    echo "   Total dependencies: " . ($results['total_dependencies'] ?? 0) . "\n";
    echo "   Outdated packages: " . count($results['outdated_packages'] ?? []) . "\n";
    echo "   Vulnerable packages: " . count($results['vulnerable_packages'] ?? []) . "\n";
    echo "   Tools used: " . implode(', ', $results['tools_used'] ?? []) . "\n";
    
    // Test Composer section
    if (isset($results['composer'])) {
        echo "\n📦 Composer:\n";
        echo "   Dependencies: " . count($results['composer']['dependencies'] ?? []) . "\n";
        echo "   Outdated: " . count($results['composer']['outdated'] ?? []) . "\n";
        echo "   Vulnerabilities: " . count($results['composer']['vulnerabilities'] ?? []) . "\n";
        
        // Show some outdated packages
        if (!empty($results['composer']['outdated'])) {
            echo "   Sample outdated packages:\n";
            foreach (array_slice($results['composer']['outdated'], 0, 3) as $package) {
                echo "     - {$package['name']}: {$package['current_version']} → {$package['latest_version']}\n";
            }
        }
    }
    
    // Test NPM section
    if (isset($results['npm'])) {
        echo "\n📦 NPM:\n";
        echo "   Dependencies: " . count($results['npm']['dependencies'] ?? []) . "\n";
        echo "   Outdated: " . count($results['npm']['outdated'] ?? []) . "\n";
        echo "   Vulnerabilities: " . count($results['npm']['vulnerabilities'] ?? []) . "\n";
    }
    
    echo "\n✅ DependencyScanner test completed successfully!\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
    
    if ($e->getPrevious()) {
        echo "   Previous: " . $e->getPrevious()->getMessage() . "\n";
    }
    
    exit(1);
}

// Test calculatePackageAge method with reflection
echo "\n🧪 Testing calculatePackageAge method...\n";

try {
    $reflection = new ReflectionClass($scanner);
    $method = $reflection->getMethod('calculatePackageAge');
    $method->setAccessible(true);
    
    // Test cases
    $testCases = [
        ['1.0.0', '2.0.0', 'major version difference'],
        ['1.0.0', '1.1.0', 'minor version difference'],
        ['1.0.0', '1.0.1', 'patch version difference'],
        ['2.0.0', '1.0.0', 'newer current version'],
        [null, '1.0.0', 'null current version'],
        ['1.0.0', null, 'null latest version'],
        [null, null, 'both null'],
        ['', '1.0.0', 'empty current version'],
        ['1.0.0', '', 'empty latest version'],
    ];
    
    foreach ($testCases as [$current, $latest, $description]) {
        try {
            $age = $method->invoke($scanner, $current, $latest);
            echo "   ✅ $description: $age days\n";
        } catch (\Exception $e) {
            echo "   ❌ $description: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n✅ calculatePackageAge tests completed!\n";
    
} catch (\Exception $e) {
    echo "❌ calculatePackageAge test failed: " . $e->getMessage() . "\n";
}

echo "\n🎉 All tests completed!\n";
