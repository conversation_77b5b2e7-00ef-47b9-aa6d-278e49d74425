<?php

/**
 * Comprehensive test script for all scanners
 */

require_once __DIR__ . '/../vendor/autoload.php';

echo "🧪 Laravel Checker - All Scanners Test\n";
echo "======================================\n\n";

use ZTeam\LaravelChecker\Config\Configuration;
use ZTeam\LaravelChecker\Scanner\QualityScanner;
use ZTeam\LaravelChecker\Scanner\SecurityScanner;
use ZTeam\LaravelChecker\Scanner\DependencyScanner;
use ZTeam\LaravelChecker\Scanner\CoverageScanner;

$errors = [];
$warnings = [];
$testResults = [];

// Test configuration
echo "1. Testing Configuration...\n";
try {
    $config = new Configuration();
    $excludeDirs = $config->getExcludeDirs();
    
    if (is_array($excludeDirs)) {
        echo "   ✅ Configuration loaded successfully\n";
        $testResults['config'] = true;
    } else {
        $errors[] = "Configuration returned invalid data";
        echo "   ❌ Configuration failed\n";
        $testResults['config'] = false;
    }
} catch (\Exception $e) {
    $errors[] = "Configuration error: " . $e->getMessage();
    echo "   ❌ Configuration error\n";
    $testResults['config'] = false;
}

// Test QualityScanner
echo "\n2. Testing QualityScanner...\n";
try {
    $scanner = new QualityScanner($config);
    
    // Create test directory
    $testDir = sys_get_temp_dir() . '/laravel-checker-quality-test';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }
    
    // Create test PHP file
    file_put_contents($testDir . '/test.php', '<?php echo "Hello World";');
    
    $results = $scanner->scan($testDir);
    
    if (is_array($results) && isset($results['total_files'])) {
        echo "   ✅ QualityScanner working\n";
        $testResults['quality'] = true;
    } else {
        $warnings[] = "QualityScanner returned unexpected results";
        echo "   ⚠️  QualityScanner warning\n";
        $testResults['quality'] = false;
    }
    
    // Cleanup
    unlink($testDir . '/test.php');
    rmdir($testDir);
    
} catch (\Exception $e) {
    $warnings[] = "QualityScanner error: " . $e->getMessage();
    echo "   ⚠️  QualityScanner error\n";
    $testResults['quality'] = false;
}

// Test SecurityScanner
echo "\n3. Testing SecurityScanner...\n";
try {
    $scanner = new SecurityScanner($config);
    
    // Create test directory
    $testDir = sys_get_temp_dir() . '/laravel-checker-security-test';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }
    
    // Create test PHP file with security issue
    file_put_contents($testDir . '/test.php', '<?php echo $_GET["input"];');
    
    $results = $scanner->scan($testDir);
    
    if (is_array($results) && isset($results['vulnerabilities'])) {
        echo "   ✅ SecurityScanner working\n";
        $testResults['security'] = true;
    } else {
        $warnings[] = "SecurityScanner returned unexpected results";
        echo "   ⚠️  SecurityScanner warning\n";
        $testResults['security'] = false;
    }
    
    // Cleanup
    unlink($testDir . '/test.php');
    rmdir($testDir);
    
} catch (\Exception $e) {
    $warnings[] = "SecurityScanner error: " . $e->getMessage();
    echo "   ⚠️  SecurityScanner error\n";
    $testResults['security'] = false;
}

// Test DependencyScanner
echo "\n4. Testing DependencyScanner...\n";
try {
    $scanner = new DependencyScanner($config);
    
    // Test with current project (has composer.json)
    $results = $scanner->scan(__DIR__ . '/..');
    
    if (is_array($results) && isset($results['total_dependencies'])) {
        echo "   ✅ DependencyScanner working\n";
        $testResults['dependency'] = true;
    } else {
        $warnings[] = "DependencyScanner returned unexpected results";
        echo "   ⚠️  DependencyScanner warning\n";
        $testResults['dependency'] = false;
    }
    
} catch (\Exception $e) {
    $warnings[] = "DependencyScanner error: " . $e->getMessage();
    echo "   ⚠️  DependencyScanner error\n";
    $testResults['dependency'] = false;
}

// Test CoverageScanner
echo "\n5. Testing CoverageScanner...\n";
try {
    $scanner = new CoverageScanner($config);
    
    // Create test directory
    $testDir = sys_get_temp_dir() . '/laravel-checker-coverage-test';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }
    
    $results = $scanner->scan($testDir);
    
    if (is_array($results) && isset($results['coverage_percentage'])) {
        echo "   ✅ CoverageScanner working\n";
        $testResults['coverage'] = true;
    } else {
        $warnings[] = "CoverageScanner returned unexpected results";
        echo "   ⚠️  CoverageScanner warning\n";
        $testResults['coverage'] = false;
    }
    
    // Cleanup
    rmdir($testDir);
    
} catch (\Exception $e) {
    $warnings[] = "CoverageScanner error: " . $e->getMessage();
    echo "   ⚠️  CoverageScanner error\n";
    $testResults['coverage'] = false;
}

// Test main LaravelChecker
echo "\n6. Testing LaravelChecker integration...\n";
try {
    $checker = new \ZTeam\LaravelChecker\LaravelChecker($config);
    
    // Create minimal test project
    $testDir = sys_get_temp_dir() . '/laravel-checker-integration-test';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }
    
    file_put_contents($testDir . '/test.php', '<?php class Test {}');
    
    $results = $checker->scan($testDir);
    
    if (is_array($results) && isset($results['summary'])) {
        echo "   ✅ LaravelChecker integration working\n";
        $testResults['integration'] = true;
    } else {
        $errors[] = "LaravelChecker integration failed";
        echo "   ❌ LaravelChecker integration failed\n";
        $testResults['integration'] = false;
    }
    
    // Cleanup
    unlink($testDir . '/test.php');
    rmdir($testDir);
    
} catch (\Exception $e) {
    $errors[] = "LaravelChecker integration error: " . $e->getMessage();
    echo "   ❌ LaravelChecker integration error\n";
    $testResults['integration'] = false;
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 Test Summary\n";
echo str_repeat("=", 50) . "\n";

$passedTests = array_sum($testResults);
$totalTests = count($testResults);

echo "Tests passed: $passedTests/$totalTests\n\n";

foreach ($testResults as $test => $passed) {
    $status = $passed ? '✅' : '❌';
    echo "$status " . ucfirst($test) . "Scanner\n";
}

if (!empty($errors)) {
    echo "\n❌ Critical Errors:\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  Warnings:\n";
    foreach ($warnings as $warning) {
        echo "   - $warning\n";
    }
}

echo "\n";

if (empty($errors)) {
    if (empty($warnings)) {
        echo "🎉 All tests passed! Laravel Checker is fully functional.\n";
    } else {
        echo "✅ Core functionality working. Some optional features may be limited.\n";
    }
    exit(0);
} else {
    echo "❌ Critical issues found. Please check the errors above.\n";
    exit(1);
}
