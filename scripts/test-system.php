<?php

/**
 * System test script for Laravel Checker
 */

require_once __DIR__ . '/../vendor/autoload.php';

echo "🧪 Laravel Checker System Test\n";
echo "==============================\n\n";

$errors = [];
$warnings = [];

// Test 1: Check autoloader
echo "1. Testing autoloader...\n";
try {
    $checker = new \ZTeam\LaravelChecker\LaravelChecker(
        new \ZTeam\LaravelChecker\Config\Configuration()
    );
    echo "   ✅ Autoloader working\n";
} catch (\Exception $e) {
    $errors[] = "Autoloader failed: " . $e->getMessage();
    echo "   ❌ Autoloader failed\n";
}

// Test 2: Check PhpParser
echo "\n2. Testing PhpParser...\n";
try {
    if (class_exists('PhpParser\ParserFactory')) {
        $factory = new \PhpParser\ParserFactory();
        
        if (method_exists($factory, 'createForNewestSupportedVersion')) {
            $parser = $factory->createForNewestSupportedVersion();
            echo "   ✅ PhpParser 5.x working\n";
        } elseif (method_exists($factory, 'createForHostVersion')) {
            $parser = $factory->createForHostVersion();
            echo "   ✅ PhpParser 4.x working\n";
        } else {
            $warnings[] = "PhpParser version may be too old";
            echo "   ⚠️  PhpParser version may be too old\n";
        }
    } else {
        $warnings[] = "PhpParser not available";
        echo "   ⚠️  PhpParser not available\n";
    }
} catch (\Exception $e) {
    $warnings[] = "PhpParser error: " . $e->getMessage();
    echo "   ⚠️  PhpParser error\n";
}

// Test 3: Check Symfony components
echo "\n3. Testing Symfony components...\n";
$symfonyComponents = [
    'Symfony\Component\Console\Application',
    'Symfony\Component\Process\Process',
    'Symfony\Component\Finder\Finder'
];

foreach ($symfonyComponents as $component) {
    if (class_exists($component)) {
        echo "   ✅ $component available\n";
    } else {
        $errors[] = "$component not available";
        echo "   ❌ $component not available\n";
    }
}

// Test 4: Test basic scanning
echo "\n4. Testing basic scanning...\n";
try {
    $config = new \ZTeam\LaravelChecker\Config\Configuration();
    $scanner = new \ZTeam\LaravelChecker\Scanner\QualityScanner($config);
    
    // Create a simple test file
    $testDir = sys_get_temp_dir() . '/laravel-checker-test';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }
    
    $testFile = $testDir . '/test.php';
    file_put_contents($testFile, '<?php echo "Hello World";');
    
    $results = $scanner->scan($testDir);
    
    if (is_array($results)) {
        echo "   ✅ Basic scanning working\n";
    } else {
        $errors[] = "Scanning returned invalid results";
        echo "   ❌ Scanning returned invalid results\n";
    }
    
    // Cleanup
    unlink($testFile);
    rmdir($testDir);
    
} catch (\Exception $e) {
    $warnings[] = "Scanning test failed: " . $e->getMessage();
    echo "   ⚠️  Scanning test failed\n";
}

// Test 5: Test configuration
echo "\n5. Testing configuration...\n";
try {
    $config = new \ZTeam\LaravelChecker\Config\Configuration();
    $excludeDirs = $config->getExcludeDirs();
    
    if (is_array($excludeDirs)) {
        echo "   ✅ Configuration loading working\n";
    } else {
        $errors[] = "Configuration returned invalid data";
        echo "   ❌ Configuration returned invalid data\n";
    }
} catch (\Exception $e) {
    $errors[] = "Configuration failed: " . $e->getMessage();
    echo "   ❌ Configuration failed\n";
}

// Test 6: Test reporters
echo "\n6. Testing reporters...\n";
$reporters = [
    'ZTeam\LaravelChecker\Reporter\JsonReporter',
    'ZTeam\LaravelChecker\Reporter\ConsoleReporter'
];

foreach ($reporters as $reporterClass) {
    if (class_exists($reporterClass)) {
        echo "   ✅ $reporterClass available\n";
    } else {
        $errors[] = "$reporterClass not available";
        echo "   ❌ $reporterClass not available\n";
    }
}

// Test HTML reporter (may fail if Twig not available)
if (class_exists('ZTeam\LaravelChecker\Reporter\HtmlReporter')) {
    if (class_exists('Twig\Environment')) {
        echo "   ✅ HtmlReporter available (with Twig)\n";
    } else {
        $warnings[] = "HtmlReporter available but Twig missing";
        echo "   ⚠️  HtmlReporter available but Twig missing\n";
    }
} else {
    $warnings[] = "HtmlReporter not available";
    echo "   ⚠️  HtmlReporter not available\n";
}

// Summary
echo "\n" . str_repeat("=", 40) . "\n";
echo "📊 Test Summary\n";
echo str_repeat("=", 40) . "\n";

if (empty($errors)) {
    echo "✅ All critical tests passed!\n";
} else {
    echo "❌ Critical errors found:\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  Warnings:\n";
    foreach ($warnings as $warning) {
        echo "   - $warning\n";
    }
}

echo "\n";

// Recommendations
if (!empty($errors)) {
    echo "🔧 Recommendations:\n";
    echo "   1. Run: composer install\n";
    echo "   2. Check: ./scripts/check-requirements.sh\n";
    echo "   3. Try: ./scripts/install.sh\n";
    echo "\n";
}

if (!empty($warnings) && empty($errors)) {
    echo "💡 Optional improvements:\n";
    echo "   1. Install Twig for HTML reports: composer require twig/twig\n";
    echo "   2. Update PhpParser: composer require nikic/php-parser:^5.0\n";
    echo "\n";
}

// Exit code
if (empty($errors)) {
    echo "🎉 System is ready to use!\n";
    exit(0);
} else {
    echo "❌ System has critical issues\n";
    exit(1);
}
