#!/bin/bash

# Laravel Checker Installation Script
# This script automates the installation process

echo "🚀 Laravel Checker - Automated Installation"
echo "==========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "composer.json" ] || [ ! -f "bin/laravel-checker" ]; then
    echo -e "${RED}❌ Error: Please run this script from the Laravel Checker root directory${NC}"
    echo "   Expected files: composer.json, bin/laravel-checker"
    exit 1
fi

# Step 1: Check requirements
echo -e "${BLUE}📋 Step 1: Checking requirements...${NC}"
if [ -f "scripts/check-requirements.sh" ]; then
    ./scripts/check-requirements.sh
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Requirements check failed. Please install missing dependencies.${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  Requirements check script not found, proceeding anyway...${NC}"
fi

echo ""

# Step 2: Choose installation method
echo -e "${BLUE}📦 Step 2: Installing dependencies...${NC}"

# Try full installation first
echo "Attempting full installation with all features..."
if composer install --no-dev --quiet 2>/dev/null; then
    echo -e "${GREEN}✅ Full installation successful${NC}"
    INSTALL_TYPE="full"
else
    echo -e "${YELLOW}⚠️  Full installation failed, trying minimal installation...${NC}"
    
    # Backup original composer.json
    cp composer.json composer.json.backup
    
    # Use minimal composer.json
    if [ -f "composer-minimal.json" ]; then
        cp composer-minimal.json composer.json
        
        if composer install --no-dev --quiet 2>/dev/null; then
            echo -e "${GREEN}✅ Minimal installation successful${NC}"
            INSTALL_TYPE="minimal"
        else
            echo -e "${RED}❌ Installation failed${NC}"
            # Restore original composer.json
            cp composer.json.backup composer.json
            rm composer.json.backup
            exit 1
        fi
    else
        echo -e "${RED}❌ Minimal composer.json not found${NC}"
        exit 1
    fi
fi

echo ""

# Step 3: Set permissions
echo -e "${BLUE}🔧 Step 3: Setting permissions...${NC}"
chmod +x bin/laravel-checker
chmod +x demo/run-demo.sh
chmod +x scripts/*.sh
echo -e "${GREEN}✅ Permissions set${NC}"

echo ""

# Step 4: Verify installation
echo -e "${BLUE}🧪 Step 4: Verifying installation...${NC}"

# Test PhpParser compatibility
if [ -f "scripts/test-phpparser.php" ]; then
    echo "Testing PhpParser compatibility..."
    if php scripts/test-phpparser.php >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PhpParser is working${NC}"
    else
        echo -e "${YELLOW}⚠️  PhpParser test failed, but continuing...${NC}"
    fi
fi

# Test basic functionality
if php bin/laravel-checker --help >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Laravel Checker CLI is working${NC}"
else
    echo -e "${RED}❌ Laravel Checker CLI test failed${NC}"
    exit 1
fi

# Check if demo project exists
if [ -d "demo/test-project" ]; then
    echo -e "${GREEN}✅ Demo project is available${NC}"
else
    echo -e "${YELLOW}⚠️  Demo project not found${NC}"
fi

echo ""

# Step 5: Installation summary
echo -e "${BLUE}📊 Installation Summary${NC}"
echo "======================="
echo -e "Installation Type: ${GREEN}$INSTALL_TYPE${NC}"
echo -e "PHP Version: ${GREEN}$(php -r 'echo PHP_VERSION;')${NC}"
echo -e "Composer Version: ${GREEN}$(composer --version --no-ansi | head -n1)${NC}"

if [ "$INSTALL_TYPE" = "minimal" ]; then
    echo ""
    echo -e "${YELLOW}⚠️  Note: Minimal installation completed${NC}"
    echo "   Some advanced features may not be available:"
    echo "   - HTML report generation (requires Twig)"
    echo "   - YAML configuration support"
    echo "   - HTTP client features"
    echo ""
    echo "   To enable full features, install missing dependencies:"
    echo "   composer require twig/twig symfony/yaml guzzlehttp/guzzle"
fi

echo ""

# Step 6: Next steps
echo -e "${BLUE}🎯 Next Steps${NC}"
echo "============="
echo "1. Run the demo:"
echo "   ./demo/run-demo.sh"
echo ""
echo "2. Scan your own project:"
echo "   ./bin/laravel-checker /path/to/your/project"
echo ""
echo "3. Generate HTML report:"
echo "   ./bin/laravel-checker --format=html --output=./reports"
echo ""
echo "4. View help:"
echo "   ./bin/laravel-checker --help"
echo ""
echo "5. Read documentation:"
echo "   - README.md (Overview)"
echo "   - INSTALL.md (Installation guide)"
echo "   - USAGE.md (Usage examples)"

echo ""
echo -e "${GREEN}🎉 Installation completed successfully!${NC}"

# Cleanup
if [ -f "composer.json.backup" ]; then
    rm composer.json.backup
fi

exit 0
