<?php

/**
 * Test script for PhpParser compatibility
 */

require_once __DIR__ . '/../vendor/autoload.php';

use PhpParser\ParserFactory;
use PhpParser\Error;

echo "🧪 Testing PhpParser compatibility...\n";
echo "=====================================\n\n";

// Test code to parse
$testCode = '<?php
class TestClass {
    public function testMethod() {
        return "Hello World";
    }
}';

try {
    $factory = new ParserFactory();
    
    echo "📋 Available ParserFactory methods:\n";
    $methods = get_class_methods($factory);
    foreach ($methods as $method) {
        echo "  - $method\n";
    }
    echo "\n";
    
    // Try different creation methods
    $parser = null;
    
    if (method_exists($factory, 'createForNewestSupportedVersion')) {
        echo "✅ Using createForNewestSupportedVersion() (PhpParser 5.x)\n";
        $parser = $factory->createForNewestSupportedVersion();
    } elseif (method_exists($factory, 'createForHostVersion')) {
        echo "✅ Using createForHostVersion() (PhpParser 4.x)\n";
        $parser = $factory->createForHostVersion();
    } elseif (method_exists($factory, 'create')) {
        echo "✅ Using create() method (PhpParser 4.x older)\n";
        
        // Check available constants
        if (defined('PhpParser\ParserFactory::PREFER_PHP7')) {
            echo "  - Using PREFER_PHP7 constant\n";
            $parser = $factory->create(ParserFactory::PREFER_PHP7);
        } elseif (defined('PhpParser\ParserFactory::ONLY_PHP7')) {
            echo "  - Using ONLY_PHP7 constant\n";
            $parser = $factory->create(ParserFactory::ONLY_PHP7);
        } else {
            echo "  - Using default create()\n";
            $parser = $factory->create();
        }
    } else {
        throw new \RuntimeException('No suitable parser creation method found');
    }
    
    echo "\n";
    
    // Test parsing
    echo "🔍 Testing code parsing...\n";
    $ast = $parser->parse($testCode);
    
    if ($ast) {
        echo "✅ Successfully parsed test code\n";
        echo "📊 AST contains " . count($ast) . " top-level nodes\n";
        
        // Test NodeFinder
        if (class_exists('PhpParser\NodeFinder')) {
            $nodeFinder = new \PhpParser\NodeFinder();
            $classes = $nodeFinder->findInstanceOf($ast, \PhpParser\Node\Stmt\Class_::class);
            $methods = $nodeFinder->findInstanceOf($ast, \PhpParser\Node\Stmt\ClassMethod::class);
            
            echo "✅ Found " . count($classes) . " classes\n";
            echo "✅ Found " . count($methods) . " methods\n";
        } else {
            echo "⚠️  NodeFinder class not available\n";
        }
    } else {
        echo "❌ Failed to parse test code\n";
    }
    
} catch (Error $e) {
    echo "❌ PhpParser Error: " . $e->getMessage() . "\n";
    echo "   Line: " . $e->getStartLine() . "\n";
} catch (\Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Check PhpParser version
try {
    $reflection = new \ReflectionClass('PhpParser\ParserFactory');
    $filename = $reflection->getFileName();
    
    if ($filename && file_exists(dirname($filename) . '/../composer.json')) {
        $composerData = json_decode(file_get_contents(dirname($filename) . '/../composer.json'), true);
        if (isset($composerData['version'])) {
            echo "📦 PhpParser version: " . $composerData['version'] . "\n";
        }
    }
    
    // Try to get version from constants or other methods
    if (defined('PhpParser\ParserFactory::VERSION')) {
        echo "📦 PhpParser version: " . \PhpParser\ParserFactory::VERSION . "\n";
    }
    
} catch (\Exception $e) {
    echo "⚠️  Could not determine PhpParser version\n";
}

echo "\n";
echo "🎯 Test completed!\n";

// Return appropriate exit code
if (isset($parser) && $parser !== null) {
    echo "✅ PhpParser is working correctly\n";
    exit(0);
} else {
    echo "❌ PhpParser test failed\n";
    exit(1);
}
