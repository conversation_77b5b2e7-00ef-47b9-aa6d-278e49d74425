<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'phpDocumentor\\Reflection\\' => array($vendorDir . '/phpdocumentor/reflection-common/src', $vendorDir . '/phpdocumentor/type-resolver/src', $vendorDir . '/phpdocumentor/reflection-docblock/src'),
    'ZTeam\\LaravelChecker\\Tests\\' => array($baseDir . '/tests'),
    'ZTeam\\LaravelChecker\\' => array($baseDir . '/src'),
    'XdgBaseDir\\' => array($vendorDir . '/dnoegel/php-xdg-base-dir/src'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Twig\\' => array($vendorDir . '/twig/twig/src'),
    'Symfony\\Polyfill\\Php84\\' => array($vendorDir . '/symfony/polyfill-php84'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\Filesystem\\' => array($vendorDir . '/symfony/filesystem'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Spatie\\ArrayToXml\\' => array($vendorDir . '/spatie/array-to-xml/src'),
    'Revolt\\' => array($vendorDir . '/revolt/event-loop/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psalm\\' => array($vendorDir . '/vimeo/psalm/src/Psalm'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PHPStan\\PhpDocParser\\' => array($vendorDir . '/phpstan/phpdoc-parser/src'),
    'LibDNS\\' => array($vendorDir . '/daverandom/libdns/src'),
    'League\\Uri\\' => array($vendorDir . '/league/uri-interfaces', $vendorDir . '/league/uri'),
    'LanguageServerProtocol\\' => array($vendorDir . '/felixfbecker/language-server-protocol/src'),
    'Kelunik\\Certificate\\' => array($vendorDir . '/kelunik/certificate/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Fidry\\CpuCoreCounter\\' => array($vendorDir . '/fidry/cpu-core-counter/src'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/src'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Composer\\XdebugHandler\\' => array($vendorDir . '/composer/xdebug-handler/src'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Pcre\\' => array($vendorDir . '/composer/pcre/src'),
    'Amp\\Sync\\' => array($vendorDir . '/amphp/sync/src'),
    'Amp\\Socket\\' => array($vendorDir . '/amphp/socket/src'),
    'Amp\\Serialization\\' => array($vendorDir . '/amphp/serialization/src'),
    'Amp\\Process\\' => array($vendorDir . '/amphp/process/src'),
    'Amp\\Pipeline\\' => array($vendorDir . '/amphp/pipeline/src'),
    'Amp\\Parser\\' => array($vendorDir . '/amphp/parser/src'),
    'Amp\\Parallel\\' => array($vendorDir . '/amphp/parallel/src'),
    'Amp\\Dns\\' => array($vendorDir . '/amphp/dns/src'),
    'Amp\\Cache\\' => array($vendorDir . '/amphp/cache/src'),
    'Amp\\ByteStream\\' => array($vendorDir . '/amphp/byte-stream/src'),
    'Amp\\' => array($vendorDir . '/amphp/amp/src'),
    'AdvancedJsonRpc\\' => array($vendorDir . '/danog/advanced-json-rpc/lib'),
);
