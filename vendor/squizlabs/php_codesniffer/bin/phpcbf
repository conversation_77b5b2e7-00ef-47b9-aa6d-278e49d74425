#!/usr/bin/env php
<?php
/**
 * PHP Code Beautifier and Fixer fixes violations of a defined coding standard.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2006-2015 Squiz Pty Ltd (ABN **************)
 * @license   https://github.com/PHPCSStandards/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

require_once __DIR__.'/../autoload.php';

$runner   = new PHP_CodeSniffer\Runner();
$exitCode = $runner->runPHPCBF();
exit($exitCode);
