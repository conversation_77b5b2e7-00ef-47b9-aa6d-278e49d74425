{"name": "zteam/laravel-checker", "description": "Comprehensive web project scanner for quality, security, and release readiness assessment", "type": "project", "license": "MIT", "authors": [{"name": "ZTeam", "email": "<EMAIL>"}], "require": {"php": "^8.0", "symfony/console": "^5.4", "symfony/process": "^5.4", "symfony/finder": "^5.4"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"ZTeam\\LaravelChecker\\": "src/"}}, "autoload-dev": {"psr-4": {"ZTeam\\LaravelChecker\\Tests\\": "tests/"}}, "bin": ["bin/laravel-checker"], "scripts": {"test": "phpunit", "check-syntax": "find src -name '*.php' -exec php -l {} \\;"}, "config": {"sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true}