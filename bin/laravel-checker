#!/usr/bin/env php
<?php

/**
 * <PERSON>vel Checker CLI Tool
 * 
 * A comprehensive web project scanner for quality, security, and release readiness assessment.
 * 
 * <AUTHOR>
 * @license MIT
 */

declare(strict_types=1);

// Find the autoloader
$autoloadPaths = [
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../../autoload.php',
    __DIR__ . '/../../autoload.php',
    __DIR__ . '/../autoload.php'
];

$autoloadFound = false;
foreach ($autoloadPaths as $autoloadPath) {
    if (file_exists($autoloadPath)) {
        require_once $autoloadPath;
        $autoloadFound = true;
        break;
    }
}

if (!$autoloadFound) {
    fwrite(STDERR, "Error: Could not find Composer autoloader.\n");
    fwrite(STDERR, "Please run 'composer install' first.\n");
    exit(1);
}

use ZTeam\LaravelChecker\Console\Application;

try {
    $application = new Application();
    $application->run();
} catch (Exception $e) {
    fwrite(STDERR, "Error: " . $e->getMessage() . "\n");
    exit(1);
}
