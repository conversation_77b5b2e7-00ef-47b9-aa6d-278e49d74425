# Hướng dẫn sử dụng Laravel Checker

## C<PERSON>ch sử dụng cơ bản

### 1. Quét dự án hiện tại

```bash
# Quét thư mục hiện tại
./bin/laravel-checker

# Hoặc nếu đã cài đặt global
laravel-checker
```

### 2. Quét một dự án cụ thể

```bash
# Quét một thư mục khác
./bin/laravel-checker /path/to/your/project

# Ví dụ
./bin/laravel-checker ~/Projects/my-laravel-app
```

### 3. Tùy chọn định dạng báo cáo

```bash
# Chỉ hiển thị trên console (mặc định)
./bin/laravel-checker --format=console

# Tạo báo cáo JSON
./bin/laravel-checker --format=json

# Tạo báo cáo HTML
./bin/laravel-checker --format=html

# Tạo nhiều định dạng cùng lúc
./bin/laravel-checker --format=json --format=html --format=console
```

### 4. Chỉ định thư mục đầu ra

```bash
# Lưu báo cáo vào thư mục cụ thể
./bin/laravel-checker --output=./quality-reports

# Tạo báo cáo trong thư mục tùy chỉnh
./bin/laravel-checker --format=html --output=/tmp/reports
```

## Tùy chọn nâng cao

### 1. Bỏ qua một số loại kiểm tra

```bash
# Bỏ qua kiểm tra bảo mật
./bin/laravel-checker --skip-security

# Bỏ qua kiểm tra chất lượng mã
./bin/laravel-checker --skip-quality

# Bỏ qua kiểm tra dependencies
./bin/laravel-checker --skip-dependencies

# Bỏ qua kiểm tra test coverage
./bin/laravel-checker --skip-coverage

# Kết hợp nhiều tùy chọn
./bin/laravel-checker --skip-security --skip-coverage
```

### 2. Loại trừ thư mục

```bash
# Loại trừ thư mục cụ thể
./bin/laravel-checker --exclude=storage --exclude=public/uploads

# Loại trừ nhiều thư mục
./bin/laravel-checker --exclude=vendor --exclude=node_modules --exclude=tests
```

### 3. Sử dụng file cấu hình

```bash
# Sử dụng file cấu hình tùy chỉnh
./bin/laravel-checker --config=./my-config.json

# Sử dụng cấu hình cho môi trường cụ thể
./bin/laravel-checker --config=./config/production.json
```

## File cấu hình

### Tạo file cấu hình

Tạo file `.laravel-checker.json` trong thư mục gốc của dự án:

```json
{
    "exclude_dirs": [
        "vendor",
        "node_modules",
        ".git",
        "storage/logs",
        "storage/cache",
        "bootstrap/cache"
    ],
    "quality_rules": {
        "max_complexity": 8,
        "max_method_length": 40,
        "max_class_length": 400,
        "min_test_coverage": 80
    },
    "security_rules": {
        "check_sql_injection": true,
        "check_xss": true,
        "check_csrf": true,
        "check_file_upload": true
    },
    "tools": {
        "phpcs": {
            "enabled": true,
            "standard": "PSR12"
        },
        "phpstan": {
            "enabled": true,
            "level": 6
        },
        "eslint": {
            "enabled": true
        }
    }
}
```

### Cấu hình cho Laravel

```json
{
    "exclude_dirs": [
        "vendor",
        "node_modules",
        "storage/logs",
        "storage/framework/cache",
        "storage/framework/sessions",
        "storage/framework/views",
        "bootstrap/cache",
        "public/storage"
    ],
    "quality_rules": {
        "max_complexity": 10,
        "max_method_length": 50,
        "min_test_coverage": 70
    },
    "laravel_specific": {
        "check_env_example": true,
        "check_middleware": true,
        "check_policies": true,
        "forbidden_functions": ["dd", "dump", "var_dump"]
    }
}
```

## Hiểu kết quả báo cáo

### 1. Điểm số (Scores)

- **Overall Score**: Điểm tổng thể (0-100)
- **Quality Score**: Chất lượng mã (0-100)
- **Security Score**: Bảo mật (0-100)
- **Coverage Score**: Độ bao phủ test (0-100)
- **Dependency Score**: Chất lượng dependencies (0-100)

### 2. Mức độ nghiêm trọng

- **Critical**: Cần sửa ngay lập tức
- **High**: Ưu tiên cao
- **Medium**: Ưu tiên trung bình
- **Low**: Ưu tiên thấp

### 3. Release Readiness

- **Ready**: Sẵn sàng release
- **Ready with Warnings**: Có thể release nhưng có cảnh báo
- **Not Ready**: Chưa sẵn sàng release

## Tích hợp CI/CD

### GitHub Actions

```yaml
- name: Run Laravel Checker
  run: |
    composer global require zteam/laravel-checker
    laravel-checker --format=json --output=./reports
    
- name: Check Quality Gates
  run: |
    SCORE=$(cat reports/summary.json | jq '.overall_score')
    if (( $(echo "$SCORE < 60" | bc -l) )); then
      echo "Quality score too low: $SCORE"
      exit 1
    fi
```

### Jenkins

```groovy
stage('Quality Check') {
    steps {
        sh 'composer global require zteam/laravel-checker'
        sh 'laravel-checker --format=html --output=./reports'
        
        publishHTML([
            reportDir: 'reports',
            reportFiles: 'laravel-checker-report.html',
            reportName: 'Quality Report'
        ])
    }
}
```

## Xử lý kết quả

### 1. Phân tích JSON

```bash
# Lấy điểm tổng thể
cat reports/summary.json | jq '.overall_score'

# Lấy số lượng security issues
cat reports/summary.json | jq '.security_issues'

# Lấy danh sách recommendations
cat reports/summary.json | jq '.recommendations'
```

### 2. Thiết lập Quality Gates

```bash
#!/bin/bash
REPORT="reports/summary.json"

OVERALL_SCORE=$(cat $REPORT | jq '.overall_score')
SECURITY_ISSUES=$(cat $REPORT | jq '.security_issues')
VULNERABLE_DEPS=$(cat $REPORT | jq '.vulnerable_deps')

# Kiểm tra các điều kiện
if [ "$SECURITY_ISSUES" -gt 0 ]; then
    echo "❌ Security issues found"
    exit 1
fi

if [ "$VULNERABLE_DEPS" -gt 0 ]; then
    echo "❌ Vulnerable dependencies found"
    exit 1
fi

if (( $(echo "$OVERALL_SCORE < 70" | bc -l) )); then
    echo "❌ Quality score too low: $OVERALL_SCORE"
    exit 1
fi

echo "✅ All quality gates passed"
```

## Troubleshooting

### 1. Lỗi thường gặp

```bash
# Lỗi memory limit
php -d memory_limit=512M bin/laravel-checker

# Lỗi timeout
./bin/laravel-checker --timeout=600

# Lỗi permission
chmod +x bin/laravel-checker
```

### 2. Debug mode

```bash
# Chạy với verbose output
./bin/laravel-checker --verbose

# Chạy với debug mode
./bin/laravel-checker --debug
```

### 3. Kiểm tra cấu hình

```bash
# Hiển thị cấu hình hiện tại
./bin/laravel-checker --show-config

# Validate file cấu hình
./bin/laravel-checker --validate-config=./config.json
```

## Best Practices

### 1. Cấu hình cho team

- Tạo file `.laravel-checker.json` trong repository
- Commit file cấu hình để đồng bộ giữa các thành viên
- Thiết lập quality gates phù hợp với dự án

### 2. Tích hợp workflow

- Chạy trong pre-commit hooks
- Tích hợp vào CI/CD pipeline
- Thiết lập notifications cho team

### 3. Monitoring

- Theo dõi trends của quality scores
- Thiết lập alerts cho security issues
- Review báo cáo định kỳ

## Ví dụ thực tế

### Quét dự án Laravel

```bash
# Quét dự án Laravel với cấu hình tối ưu
./bin/laravel-checker \
    --config=./config/laravel.json \
    --format=html \
    --format=json \
    --output=./quality-reports \
    --exclude=storage \
    --exclude=bootstrap/cache
```

### Quét cho production

```bash
# Quét với tiêu chuẩn cao cho production
./bin/laravel-checker \
    --config=./config/production.json \
    --format=json \
    --output=./reports && \
    
# Kiểm tra quality gates
./scripts/check-quality-gates.sh
```
